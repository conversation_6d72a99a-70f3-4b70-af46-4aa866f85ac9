import type { CuttingTool, VoxelCoordinate } from '@/types'
import { voxelOptimizer } from './voxelPerformanceOptimizer'

/**
 * VoxelToolGenerator creates voxelized representations of cutting tools
 * All tools are aligned with Z-axis, centered at base (tip at z=0)
 */
export class VoxelToolGenerator {
  private resolution: number

  constructor(resolution: number = 0.5) {
    this.resolution = resolution
  }

  /**
   * Generate voxel offsets for a cylindrical endmill
   * @param diameter Tool diameter in mm
   * @param height Tool height in mm
   * @returns Array of relative voxel positions
   */
  generateEndmill(diameter: number, height: number): VoxelCoordinate[] {
    const offsets: VoxelCoordinate[] = []
    const radiusVoxels = Math.ceil((diameter / 2) / this.resolution)
    const heightVoxels = Math.ceil(height / this.resolution)

    // Generate cylinder shape
    for (let z = 0; z < heightVoxels; z++) {
      for (let x = -radiusVoxels; x <= radiusVoxels; x++) {
        for (let y = -radiusVoxels; y <= radiusVoxels; y++) {
          const distance = Math.sqrt(x * x + y * y)
          if (distance <= radiusVoxels) {
            offsets.push({ x, y, z })
          }
        }
      }
    }

    return offsets
  }

  /**
   * Generate voxel offsets for a ballnose endmill
   * @param diameter Tool diameter in mm
   * @param height Tool height in mm
   * @returns Array of relative voxel positions
   */
  generateBallnose(diameter: number, height: number): VoxelCoordinate[] {
    const offsets: VoxelCoordinate[] = []
    const radiusVoxels = Math.ceil((diameter / 2) / this.resolution)
    const heightVoxels = Math.ceil(height / this.resolution)

    // Generate cylinder body
    for (let z = radiusVoxels; z < heightVoxels; z++) {
      for (let x = -radiusVoxels; x <= radiusVoxels; x++) {
        for (let y = -radiusVoxels; y <= radiusVoxels; y++) {
          const distance = Math.sqrt(x * x + y * y)
          if (distance <= radiusVoxels) {
            offsets.push({ x, y, z })
          }
        }
      }
    }

    // Generate hemisphere tip
    for (let z = 0; z < radiusVoxels; z++) {
      for (let x = -radiusVoxels; x <= radiusVoxels; x++) {
        for (let y = -radiusVoxels; y <= radiusVoxels; y++) {
          const distance = Math.sqrt(x * x + y * y + (radiusVoxels - z) * (radiusVoxels - z))
          if (distance <= radiusVoxels) {
            offsets.push({ x, y, z })
          }
        }
      }
    }

    return offsets
  }

  /**
   * Generate voxel offsets for a chamfer/V-bit tool
   * @param diameter Tool diameter at top in mm
   * @param height Tool height in mm
   * @param tipAngle Tip angle in degrees (default 90°)
   * @returns Array of relative voxel positions
   */
  generateChamfer(diameter: number, height: number, tipAngle: number = 90): VoxelCoordinate[] {
    const offsets: VoxelCoordinate[] = []
    const maxRadiusVoxels = Math.ceil((diameter / 2) / this.resolution)
    const heightVoxels = Math.ceil(height / this.resolution)
    
    // Calculate tip radius based on angle
    const halfAngleRad = (tipAngle / 2) * (Math.PI / 180)
    const tipRadius = height * Math.tan(halfAngleRad)
    const tipRadiusVoxels = Math.ceil(tipRadius / this.resolution)

    // Generate cone shape
    for (let z = 0; z < heightVoxels; z++) {
      // Linear interpolation from tip to full diameter
      const radiusAtZ = tipRadiusVoxels + (maxRadiusVoxels - tipRadiusVoxels) * (z / heightVoxels)
      
      for (let x = -Math.ceil(radiusAtZ); x <= Math.ceil(radiusAtZ); x++) {
        for (let y = -Math.ceil(radiusAtZ); y <= Math.ceil(radiusAtZ); y++) {
          const distance = Math.sqrt(x * x + y * y)
          if (distance <= radiusAtZ) {
            offsets.push({ x, y, z })
          }
        }
      }
    }

    return offsets
  }

  /**
   * Create a cutting tool with cached voxel representation
   */
  createTool(type: 'endmill' | 'ballnose' | 'chamfer', diameter: number, height: number, tipAngle?: number): CuttingTool {
    let voxelOffsets: VoxelCoordinate[]

    switch (type) {
      case 'endmill':
        voxelOffsets = this.generateEndmill(diameter, height)
        break
      case 'ballnose':
        voxelOffsets = this.generateBallnose(diameter, height)
        break
      case 'chamfer':
        voxelOffsets = this.generateChamfer(diameter, height, tipAngle || 90)
        break
      default:
        throw new Error(`Unknown tool type: ${type}`)
    }

    return {
      type,
      diameter,
      height,
      voxelOffsets
    }
  }

  /**
   * Get tool from layer name (compatible with existing layer detection)
   */
  createToolFromLayer(layerName: string, defaultHeight: number = 10): CuttingTool | null {
    // Extract diameter from layer name
    const diameterMatch = layerName.match(/(\d+(?:\.\d+)?)mm/i)
    if (!diameterMatch) return null

    const diameter = parseFloat(diameterMatch[1])

    // Determine tool type from layer name
    let type: 'endmill' | 'ballnose' | 'chamfer' = 'endmill'
    let tipAngle = 90

    if (layerName.toLowerCase().includes('v') || layerName.toLowerCase().includes('chamfer')) {
      type = 'chamfer'
      // Extract angle if present
      const angleMatch = layerName.match(/v(\d+)/i)
      if (angleMatch) {
        tipAngle = parseInt(angleMatch[1])
      }
    } else if (layerName.toLowerCase().includes('ball')) {
      type = 'ballnose'
    }

    return this.createTool(type, diameter, defaultHeight, tipAngle)
  }

  /**
   * Optimize tool representation by removing redundant voxels
   */
  optimizeToolOffsets(offsets: VoxelCoordinate[]): VoxelCoordinate[] {
    // Remove duplicates
    const uniqueOffsets = new Map<string, VoxelCoordinate>()
    
    for (const offset of offsets) {
      const key = `${offset.x},${offset.y},${offset.z}`
      uniqueOffsets.set(key, offset)
    }

    return Array.from(uniqueOffsets.values())
  }

  /**
   * Get tool statistics
   */
  getToolStats(tool: CuttingTool): { voxelCount: number; volumeMM3: number } {
    const voxelCount = tool.voxelOffsets?.length || 0
    const voxelVolume = this.resolution * this.resolution * this.resolution
    const volumeMM3 = voxelCount * voxelVolume

    return { voxelCount, volumeMM3 }
  }
}

/**
 * Pre-defined tool library with common sizes
 */
export class VoxelToolLibrary {
  private generator: VoxelToolGenerator
  private cache: Map<string, CuttingTool> = new Map()

  constructor(resolution: number = 0.5) {
    this.generator = new VoxelToolGenerator(resolution)
  }

  /**
   * Get or create a tool with caching and performance optimization
   */
  getTool(type: 'endmill' | 'ballnose' | 'chamfer', diameter: number, height: number = 10, tipAngle: number = 90): CuttingTool {
    const key = `${type}-${diameter}-${height}-${tipAngle}`

    // Check performance optimizer cache first
    const cachedTool = voxelOptimizer.getCachedTool(key)
    if (cachedTool) {
      return cachedTool
    }

    // Check local cache
    if (!this.cache.has(key)) {
      const tool = this.generator.createTool(type, diameter, height, tipAngle)
      this.cache.set(key, tool)

      // Also cache in performance optimizer
      voxelOptimizer.cacheTool(key, tool)
    }

    const tool = this.cache.get(key)!
    voxelOptimizer.cacheTool(key, tool)
    return tool
  }

  /**
   * Get common endmill sizes
   */
  getCommonEndmills(): CuttingTool[] {
    const sizes = [3, 4, 5, 6, 8, 10, 12, 15, 20, 25, 30]
    return sizes.map(diameter => this.getTool('endmill', diameter))
  }

  /**
   * Get common V-bit sizes
   */
  getCommonVBits(): CuttingTool[] {
    const configs = [
      { diameter: 6, angle: 60 },
      { diameter: 6, angle: 90 },
      { diameter: 6, angle: 120 },
      { diameter: 8, angle: 90 },
      { diameter: 10, angle: 90 },
      { diameter: 12, angle: 90 }
    ]
    return configs.map(config => this.getTool('chamfer', config.diameter, 10, config.angle))
  }

  /**
   * Clear cache to free memory
   */
  clearCache(): void {
    this.cache.clear()
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { toolCount: number; memoryEstimateMB: number } {
    const toolCount = this.cache.size
    let totalVoxels = 0
    
    for (const tool of this.cache.values()) {
      totalVoxels += tool.voxelOffsets?.length || 0
    }

    // Rough estimate: each voxel offset = 3 numbers * 4 bytes = 12 bytes
    const memoryEstimateMB = (totalVoxels * 12) / (1024 * 1024)
    
    return { toolCount, memoryEstimateMB }
  }
}

// Export singleton instance
export const voxelToolLibrary = new VoxelToolLibrary()
