# 🧊 Voxel-Based CAM Visualizer

A high-resolution voxel-based CAM (Computer-Aided Manufacturing) simulation system for door machining visualization. This system provides real-time 3D visualization of material removal operations using advanced voxel technology.

## 🎯 Features

### Core Functionality
- **High-Resolution Voxel Grids**: Configurable resolution from 0.25mm to 2.0mm per voxel
- **Multiple Tool Types**: Support for endmills, ballnose, and chamfer/V-bit tools
- **Real-Time Carving Simulation**: Live material removal visualization
- **Dual-Face Operations**: Support for both top and bottom face machining
- **Advanced Mesh Generation**: Choice between greedy meshing (fast) and marching cubes (smooth)

### Tool Support
- **Endmills**: Cylindrical cutting tools (3-30mm diameter)
- **Ballnose**: Hemisphere-tipped tools for smooth finishing
- **Chamfer/V-bits**: Conical tools for V-carving and chamfering (60°, 90°, 120°)
- **Automatic Tool Detection**: Extract tool information from layer names

### Export Capabilities
- **STL Export**: Binary and ASCII formats for 3D printing
- **GLTF Export**: Modern 3D format with materials and lighting
- **OBJ Export**: Universal 3D format for CAD software
- **JSON Export**: Voxel data export for debugging and analysis

### Performance Optimization
- **Intelligent Caching**: Tool and mesh caching for improved performance
- **Adaptive Quality**: Automatic quality adjustment based on system performance
- **Memory Management**: Efficient memory usage with monitoring
- **Batch Processing**: Optimized processing for multiple operations

## 🚀 Quick Start

### 1. Access the Voxel CAM System
1. Open the Lua Editor application
2. Load a door script (or use the test script: `tests/voxel-integration-test.lua`)
3. Run the script to generate draw commands
4. Switch to the **"Voxel CAM"** tab in the visualization panel

### 2. Configure Workpiece
- **Width/Height**: Set door dimensions (default: 600x800mm)
- **Thickness**: Set material thickness (default: 18mm)
- **Resolution**: Choose voxel resolution (0.5mm recommended for balance)

### 3. Generate and Simulate
1. Click **"Generate Grid"** to create the voxel workpiece
2. Click **"Simulate Carving"** to process all tool operations
3. View the real-time 3D visualization
4. Export results using **"Export STL"** or **"Export GLTF"**

## 🔧 Technical Architecture

### Core Components

#### VoxelGrid (`src/services/voxelGrid.ts`)
- 3D ndarray-based voxel storage
- Coordinate transformation utilities
- Memory-efficient voxel operations
- Grid manipulation and querying

#### VoxelTools (`src/services/voxelTools.ts`)
- Parametric tool generation
- Voxel-based tool representations
- Tool library with common sizes
- Layer-based tool detection

#### VoxelCarvingEngine (`src/services/voxelCarvingEngine.ts`)
- Material removal simulation
- Tool sweep operations
- Pocket and groove operations
- Multi-pass depth control

#### VoxelMeshGenerator (`src/services/voxelMeshGenerator.ts`)
- Greedy meshing algorithm (fast)
- Marching cubes algorithm (smooth)
- LOD (Level of Detail) support
- Material and texture mapping

#### VoxelExporter (`src/services/voxelExporter.ts`)
- Multi-format export support
- File size optimization
- Batch export capabilities
- Export statistics and recommendations

### Performance System

#### VoxelPerformanceOptimizer (`src/services/voxelPerformanceOptimizer.ts`)
- Multi-level caching system
- Memory usage monitoring
- Adaptive quality settings
- Performance profiling tools

## 📊 Performance Guidelines

### Resolution Recommendations
- **0.25mm**: Ultra-high quality (slow, large memory usage)
- **0.5mm**: High quality (recommended for most use cases)
- **1.0mm**: Medium quality (faster processing)
- **2.0mm**: Low quality (preview mode)

### Memory Usage
| Resolution | 600x800x18mm Door | Memory Usage | Processing Time |
|------------|-------------------|--------------|-----------------|
| 0.25mm     | 230M voxels      | ~230MB       | 60-120s        |
| 0.5mm      | 29M voxels       | ~29MB        | 15-30s         |
| 1.0mm      | 3.6M voxels      | ~3.6MB       | 3-8s           |
| 2.0mm      | 460K voxels      | ~460KB       | 1-2s           |

### Optimization Tips
1. **Start with 1.0mm resolution** for initial testing
2. **Use greedy meshing** for faster preview
3. **Switch to marching cubes** for final high-quality output
4. **Enable caching** for repeated operations
5. **Monitor memory usage** in the performance panel

## 🧪 Testing

### Test Suite
Run the comprehensive test suite: `tests/voxel-cam-test-suite.html`

### Integration Test
Use the integration test script: `tests/voxel-integration-test.lua`
- Creates a comprehensive door model
- Tests all tool types and operations
- Validates both top and bottom face operations

### Performance Benchmarks
Execute performance benchmarks: `tests/voxel-performance-benchmark.ts`
- Grid creation benchmarks
- Tool generation performance
- Carving operation timing
- Memory usage analysis

## 🎨 UI Components

### VoxelCAMSimulator (`src/components/VoxelCAMSimulator.vue`)
Main simulation interface with:
- Workpiece configuration controls
- Real-time progress monitoring
- Performance statistics display
- Export functionality

### VoxelViewer3D (`src/components/VoxelViewer3D.vue`)
3D visualization component featuring:
- Three.js-based rendering
- Orbit controls for navigation
- Multiple material options
- Lighting and shadow effects

## 🔍 Troubleshooting

### Common Issues

#### High Memory Usage
- Reduce voxel resolution
- Clear caches periodically
- Use greedy meshing instead of marching cubes

#### Slow Performance
- Check system memory availability
- Reduce grid size or resolution
- Enable adaptive quality settings

#### Export Failures
- Ensure sufficient memory for mesh generation
- Try exporting with lower resolution
- Check browser download permissions

### Debug Tools
- Performance monitoring panel
- Memory usage statistics
- Cache efficiency metrics
- Operation timing information

## 🚀 Future Enhancements

### Planned Features
- **Multi-threading**: Web Workers for background processing
- **Progressive Rendering**: Incremental mesh updates
- **Advanced Materials**: PBR materials and textures
- **Animation Support**: Tool path animation
- **Cloud Export**: Direct upload to cloud storage

### Performance Improvements
- **GPU Acceleration**: WebGL compute shaders
- **Compression**: Voxel data compression
- **Streaming**: Large model streaming
- **Caching**: Persistent cache storage

## 📚 API Reference

### Key Classes
- `VoxelGrid`: Core voxel storage and manipulation
- `VoxelCarvingEngine`: Material removal simulation
- `VoxelMeshGenerator`: Mesh generation algorithms
- `VoxelToolLibrary`: Tool management and caching
- `VoxelExporter`: Multi-format export system

### Configuration Options
- Grid dimensions and resolution
- Tool parameters and types
- Mesh generation algorithms
- Export formats and quality

## 🤝 Contributing

### Development Setup
1. Install dependencies: `npm install`
2. Run development server: `npm run tauri:dev`
3. Access test suite: Open `tests/voxel-cam-test-suite.html`

### Code Structure
- `/src/services/voxel*`: Core voxel system
- `/src/components/Voxel*`: UI components
- `/tests/`: Test suites and benchmarks
- `/docs/`: Documentation

---

**Built with**: TypeScript, Three.js, Vue 3, ndarray, greedy-mesher, isosurface

**Performance**: Optimized for real-time visualization with adaptive quality

**Compatibility**: Modern browsers with WebGL support
