import * as THREE from 'three'
import { STLExporter } from 'three/examples/jsm/exporters/STLExporter.js'
import { GLTFExporter } from 'three/examples/jsm/exporters/GLTFExporter.js'
import type { VoxelGrid } from './voxelGrid'
import { VoxelMeshGenerator } from './voxelMeshGenerator'

/**
 * VoxelExporter handles exporting voxel meshes to various formats
 */
export class VoxelExporter {
  private meshGenerator: VoxelMeshGenerator

  constructor(resolution: number = 0.5) {
    this.meshGenerator = new VoxelMeshGenerator(resolution)
  }

  /**
   * Export voxel grid as STL file
   */
  async exportSTL(
    grid: VoxelGrid,
    filename: string = 'voxel_model.stl',
    binary: boolean = true
  ): Promise<void> {
    try {
      // Generate mesh
      const mesh = this.meshGenerator.generateMeshMarching(grid)
      
      // Create STL exporter
      const exporter = new STLExporter()
      
      // Export to STL
      const stlData = exporter.parse(mesh, { binary })

      // Convert DataView to ArrayBuffer if needed
      const exportData = stlData instanceof DataView ? stlData.buffer : stlData

      // Download file
      this.downloadFile(exportData, filename, binary ? 'application/octet-stream' : 'text/plain')
      
      // Cleanup
      mesh.geometry.dispose()
      if (Array.isArray(mesh.material)) {
        mesh.material.forEach(mat => mat.dispose())
      } else {
        mesh.material.dispose()
      }
      
    } catch (error) {
      console.error('Failed to export STL:', error)
      throw new Error('STL export failed')
    }
  }

  /**
   * Export voxel grid as GLTF file
   */
  async exportGLTF(
    grid: VoxelGrid,
    filename: string = 'voxel_model.gltf',
    binary: boolean = false
  ): Promise<void> {
    try {
      // Generate mesh with proper material
      const mesh = this.meshGenerator.generateMeshWithMaterial(grid, {
        color: 0xd4a574,
        roughness: 0.8,
        metalness: 0.1
      }, 'marching')

      // Create scene for export
      const scene = new THREE.Scene()
      scene.add(mesh)

      // Create GLTF exporter
      const exporter = new GLTFExporter()

      // Export options
      const options = {
        binary,
        embedImages: true,
        animations: [],
        truncateDrawRange: true,
        maxTextureSize: 4096
      }

      // Export to GLTF
      const gltfData = await new Promise<ArrayBuffer | string>((resolve, reject) => {
        exporter.parse(
          scene,
          (result) => {
            // Handle different result types from GLTF exporter
            if (typeof result === 'string' || result instanceof ArrayBuffer) {
              resolve(result)
            } else {
              // Convert object to JSON string for non-binary exports
              resolve(JSON.stringify(result))
            }
          },
          (error) => reject(error),
          options
        )
      })

      // Download file
      const mimeType = binary ? 'application/octet-stream' : 'application/json'
      const extension = binary ? '.glb' : '.gltf'
      const finalFilename = filename.replace(/\.(gltf|glb)$/i, '') + extension
      
      this.downloadFile(gltfData, finalFilename, mimeType)

      // Cleanup
      mesh.geometry.dispose()
      if (Array.isArray(mesh.material)) {
        mesh.material.forEach(mat => mat.dispose())
      } else {
        mesh.material.dispose()
      }

    } catch (error) {
      console.error('Failed to export GLTF:', error)
      throw new Error('GLTF export failed')
    }
  }

  /**
   * Export voxel grid as OBJ file (simple text format)
   */
  async exportOBJ(grid: VoxelGrid, filename: string = 'voxel_model.obj'): Promise<void> {
    try {
      // Generate mesh
      const mesh = this.meshGenerator.generateMeshGreedy(grid)
      const geometry = mesh.geometry as THREE.BufferGeometry

      // Extract vertices and faces
      const positions = geometry.getAttribute('position')
      const indices = geometry.getIndex()

      if (!positions || !indices) {
        throw new Error('Invalid geometry for OBJ export')
      }

      // Build OBJ content
      let objContent = '# Voxel CAM Model\n'
      objContent += `# Generated on ${new Date().toISOString()}\n\n`

      // Write vertices
      for (let i = 0; i < positions.count; i++) {
        const x = positions.getX(i)
        const y = positions.getY(i)
        const z = positions.getZ(i)
        objContent += `v ${x.toFixed(6)} ${y.toFixed(6)} ${z.toFixed(6)}\n`
      }

      objContent += '\n'

      // Write faces (OBJ uses 1-based indexing)
      for (let i = 0; i < indices.count; i += 3) {
        const a = indices.getX(i) + 1
        const b = indices.getX(i + 1) + 1
        const c = indices.getX(i + 2) + 1
        objContent += `f ${a} ${b} ${c}\n`
      }

      // Download file
      this.downloadFile(objContent, filename, 'text/plain')

      // Cleanup
      mesh.geometry.dispose()
      if (Array.isArray(mesh.material)) {
        mesh.material.forEach(mat => mat.dispose())
      } else {
        mesh.material.dispose()
      }

    } catch (error) {
      console.error('Failed to export OBJ:', error)
      throw new Error('OBJ export failed')
    }
  }

  /**
   * Export voxel grid data as JSON for debugging
   */
  async exportVoxelData(grid: VoxelGrid, filename: string = 'voxel_data.json'): Promise<void> {
    try {
      const config = grid.getConfig()
      const dimensions = grid.getDimensions()
      const memoryStats = grid.getMemoryStats()

      // Sample voxel data (full export would be too large)
      const sampleData: number[][][] = []
      const sampleSize = Math.min(32, dimensions[0]) // Limit sample size

      for (let x = 0; x < sampleSize; x++) {
        sampleData[x] = []
        for (let y = 0; y < sampleSize; y++) {
          sampleData[x][y] = []
          for (let z = 0; z < Math.min(32, dimensions[2]); z++) {
            sampleData[x][y][z] = grid.getVoxel({ x, y, z })
          }
        }
      }

      const exportData = {
        metadata: {
          exportDate: new Date().toISOString(),
          version: '1.0',
          format: 'voxel-cam-data'
        },
        config,
        dimensions,
        memoryStats,
        sampleData,
        note: `Sample data limited to ${sampleSize}x${sampleSize}x${Math.min(32, dimensions[2])} voxels`
      }

      const jsonContent = JSON.stringify(exportData, null, 2)
      this.downloadFile(jsonContent, filename, 'application/json')

    } catch (error) {
      console.error('Failed to export voxel data:', error)
      throw new Error('Voxel data export failed')
    }
  }

  /**
   * Export multiple formats in a ZIP file (requires additional library)
   */
  async exportMultipleFormats(
    grid: VoxelGrid,
    formats: ('stl' | 'gltf' | 'obj' | 'json')[],
    baseName: string = 'voxel_model'
  ): Promise<void> {
    // This would require a ZIP library like JSZip
    // For now, export formats sequentially
    for (const format of formats) {
      switch (format) {
        case 'stl':
          await this.exportSTL(grid, `${baseName}.stl`)
          break
        case 'gltf':
          await this.exportGLTF(grid, `${baseName}.gltf`)
          break
        case 'obj':
          await this.exportOBJ(grid, `${baseName}.obj`)
          break
        case 'json':
          await this.exportVoxelData(grid, `${baseName}_data.json`)
          break
      }
      
      // Small delay between exports
      await new Promise(resolve => setTimeout(resolve, 500))
    }
  }

  /**
   * Get export statistics
   */
  getExportStats(grid: VoxelGrid): {
    estimatedFileSizes: Record<string, string>
    recommendedFormat: string
  } {
    const dimensions = grid.getDimensions()
    const totalVoxels = dimensions[0] * dimensions[1] * dimensions[2]
    
    // Rough file size estimates
    const vertexCount = totalVoxels * 0.1 // Estimate after meshing
    const faceCount = vertexCount * 0.5
    
    const stlSize = faceCount * 50 // ~50 bytes per triangle in binary STL
    const objSize = vertexCount * 30 + faceCount * 20 // Text format
    const gltfSize = stlSize * 0.7 // GLTF is more efficient
    
    const estimatedFileSizes = {
      STL: this.formatFileSize(stlSize),
      OBJ: this.formatFileSize(objSize),
      GLTF: this.formatFileSize(gltfSize),
      JSON: this.formatFileSize(totalVoxels * 1) // 1 byte per voxel
    }

    // Recommend format based on size and use case
    let recommendedFormat = 'STL'
    if (stlSize > 50 * 1024 * 1024) { // > 50MB
      recommendedFormat = 'GLTF'
    }

    return { estimatedFileSizes, recommendedFormat }
  }

  /**
   * Format file size for display
   */
  private formatFileSize(bytes: number): string {
    if (bytes < 1024) return bytes + ' B'
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB'
    if (bytes < 1024 * 1024 * 1024) return (bytes / (1024 * 1024)).toFixed(1) + ' MB'
    return (bytes / (1024 * 1024 * 1024)).toFixed(1) + ' GB'
  }

  /**
   * Download file helper
   */
  private downloadFile(data: string | ArrayBuffer, filename: string, mimeType: string): void {
    const blob = new Blob([data], { type: mimeType })
    const url = URL.createObjectURL(blob)
    
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    // Cleanup
    setTimeout(() => URL.revokeObjectURL(url), 1000)
  }
}

// Export singleton instance
export const voxelExporter = new VoxelExporter()
