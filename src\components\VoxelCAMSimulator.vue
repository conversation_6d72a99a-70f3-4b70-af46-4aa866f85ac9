<template>
  <div class="voxel-cam-simulator">
    <!-- Control Panel -->
    <div class="control-panel">
      <div class="control-section">
        <h3>Workpiece Setup</h3>
        <div class="warning-message" v-if="memoryWarning">
          ⚠️ {{ memoryWarning }}
        </div>
        <div class="control-row">
          <label>Width (mm):</label>
          <input v-model.number="workpieceWidth" type="number" min="50" max="2000" step="10" @input="updateMemoryWarning" />
        </div>
        <div class="control-row">
          <label>Height (mm):</label>
          <input v-model.number="workpieceHeight" type="number" min="50" max="2000" step="10" @input="updateMemoryWarning" />
        </div>
        <div class="control-row">
          <label>Thickness (mm):</label>
          <input v-model.number="workpieceThickness" type="number" min="6" max="50" step="1" @input="updateMemoryWarning" />
        </div>
        <div class="control-row">
          <label>Resolution (mm/voxel):</label>
          <select v-model="voxelResolution" @change="updateMemoryWarning">
            <option value="0.25">0.25mm (Ultra High - Slow)</option>
            <option value="0.5">0.5mm (High - Moderate)</option>
            <option value="1.0">1.0mm (Medium - Fast)</option>
            <option value="2.0">2.0mm (Low - Very Fast)</option>
          </select>
        </div>
      </div>

      <div class="control-section">
        <h3>Simulation</h3>
        <div class="control-row">
          <button @click="generateVoxelGrid" :disabled="isProcessing" class="primary-button">
            {{ isProcessing ? 'Processing...' : 'Generate Grid' }}
          </button>
          <button @click="simulateCarving" :disabled="!voxelGrid || isProcessing" class="primary-button">
            Simulate Carving
          </button>
          <button @click="resetSimulation" :disabled="!voxelGrid" class="secondary-button">
            Reset
          </button>
        </div>
        <div class="control-row">
          <button @click="exportSTL" :disabled="!voxelGrid || isProcessing" class="export-button">
            Export STL
          </button>
          <button @click="exportGLTF" :disabled="!voxelGrid || isProcessing" class="export-button">
            Export GLTF
          </button>
        </div>
        <div class="control-row">
          <label>Default Tool Diameter (mm):</label>
          <input v-model.number="defaultToolDiameter" type="number" min="1" max="50" step="0.5" />
        </div>
        <div class="control-row">
          <label>Default Cut Depth (mm):</label>
          <input v-model.number="defaultCutDepth" type="number" min="0.5" max="20" step="0.5" />
        </div>
      </div>

      <div class="control-section" v-if="simulationStats">
        <h3>Statistics</h3>
        <div class="stats-grid">
          <div class="stat-item">
            <span class="stat-label">Grid Size:</span>
            <span class="stat-value">{{ simulationStats.gridDimensions.join('×') }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Total Voxels:</span>
            <span class="stat-value">{{ simulationStats.totalVoxels.toLocaleString() }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Removed Volume:</span>
            <span class="stat-value">{{ simulationStats.removedVolumeMM3.toFixed(2) }} mm³</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Memory Usage:</span>
            <span class="stat-value">{{ simulationStats.memoryMB.toFixed(2) }} MB</span>
          </div>
        </div>
      </div>

      <div class="control-section" v-if="performanceStats">
        <h3>Performance</h3>
        <div class="stats-grid">
          <div class="stat-item">
            <span class="stat-label">Cache Efficiency:</span>
            <span class="stat-value">{{ performanceStats.cacheHitRate.toFixed(1) }}%</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Processing Time:</span>
            <span class="stat-value">{{ performanceStats.lastOperationTime.toFixed(0) }}ms</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Heap Memory:</span>
            <span class="stat-value">{{ performanceStats.heapUsed.toFixed(1) }} MB</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Optimization:</span>
            <span class="stat-value">{{ performanceStats.qualityLevel }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 3D Viewer -->
    <div class="viewer-container">
      <VoxelViewer3D 
        ref="voxelViewerRef"
        :voxel-grid="voxelGrid"
        :auto-regenerate="true"
      />
    </div>

    <!-- Progress Overlay -->
    <div v-if="isProcessing" class="progress-overlay">
      <div class="progress-content">
        <div class="progress-spinner"></div>
        <h3>{{ progressMessage }}</h3>
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: progressPercent + '%' }"></div>
        </div>
        <p>{{ progressPercent.toFixed(1) }}%</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, nextTick } from 'vue'
import VoxelViewer3D from './VoxelViewer3D.vue'
import { VoxelGrid, createDoorVoxelGrid } from '@/services/voxelGrid'
import { VoxelCarvingEngine } from '@/services/voxelCarvingEngine'
import { VoxelPathProcessor } from '@/services/voxelPathProcessor'
import { voxelToolLibrary } from '@/services/voxelTools'
import { voxelExporter } from '@/services/voxelExporter'
import { voxelOptimizer } from '@/services/voxelPerformanceOptimizer'
import type { DrawCommand } from '@/types'

interface Props {
  drawCommands: DrawCommand[]
  doorDimensions: {
    width: number
    height: number
    thickness: number
  }
}

const props = defineProps<Props>()

// Template refs
const voxelViewerRef = ref<InstanceType<typeof VoxelViewer3D>>()

// Reactive state with safe defaults
const workpieceWidth = ref(200) // Start with smaller default to prevent freezing
const workpieceHeight = ref(200)
const workpieceThickness = ref(18)
const voxelResolution = ref(1.0) // Start with lower resolution for better performance
const defaultToolDiameter = ref(6)
const defaultCutDepth = ref(5)

const voxelGrid = ref<VoxelGrid>()
const carvingEngine = ref<VoxelCarvingEngine>()
const isProcessing = ref(false)
const progressMessage = ref('')
const progressPercent = ref(0)

// Simulation statistics
const simulationStats = ref<{
  gridDimensions: [number, number, number]
  totalVoxels: number
  removedVolumeMM3: number
  memoryMB: number
} | null>(null)

// Performance statistics
const performanceStats = ref<{
  cacheHitRate: number
  lastOperationTime: number
  heapUsed: number
  qualityLevel: string
} | null>(null)

// Memory warning
const memoryWarning = ref('')

// Watch for door dimension changes
watch(() => props.doorDimensions, (newDimensions) => {
  if (newDimensions) {
    workpieceWidth.value = newDimensions.width
    workpieceHeight.value = newDimensions.height
    workpieceThickness.value = newDimensions.thickness
  }
}, { immediate: true })

// Generate voxel grid with proper async handling
const generateVoxelGrid = async () => {
  if (isProcessing.value) return // Prevent multiple simultaneous operations

  isProcessing.value = true
  progressMessage.value = 'Creating voxel grid...'
  progressPercent.value = 0

  const startTime = performance.now()

  try {
    // Validate input parameters
    if (workpieceWidth.value <= 0 || workpieceHeight.value <= 0 || workpieceThickness.value <= 0) {
      throw new Error('Invalid workpiece dimensions')
    }

    if (voxelResolution.value <= 0 || voxelResolution.value > 5) {
      throw new Error('Invalid voxel resolution (must be between 0.1 and 5.0mm)')
    }

    // Calculate memory requirements and warn if too large
    const estimatedVoxels = Math.ceil(workpieceWidth.value / voxelResolution.value) *
                           Math.ceil(workpieceHeight.value / voxelResolution.value) *
                           Math.ceil(workpieceThickness.value / voxelResolution.value)

    const estimatedMemoryMB = estimatedVoxels / (1024 * 1024)

    if (estimatedMemoryMB > 500) {
      throw new Error(`Grid too large (${estimatedMemoryMB.toFixed(0)}MB). Please increase resolution or reduce dimensions.`)
    }

    // Allow UI to update
    await nextTick()

    // Create new voxel grid in chunks to avoid blocking
    progressMessage.value = 'Allocating voxel memory...'
    await new Promise(resolve => setTimeout(resolve, 10))

    voxelGrid.value = createDoorVoxelGrid(
      workpieceWidth.value,
      workpieceHeight.value,
      workpieceThickness.value,
      voxelResolution.value
    )

    progressPercent.value = 50
    progressMessage.value = 'Creating carving engine...'
    await new Promise(resolve => setTimeout(resolve, 10))

    // Create carving engine
    carvingEngine.value = new VoxelCarvingEngine(voxelGrid.value)

    progressPercent.value = 100
    progressMessage.value = 'Grid created successfully!'

    const endTime = performance.now()

    // Update statistics
    updateStatistics()
    updatePerformanceStats(endTime - startTime)

    // Show success message briefly
    setTimeout(() => {
      isProcessing.value = false
    }, 1000)

  } catch (error) {
    console.error('Failed to generate voxel grid:', error)
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
    progressMessage.value = `Error: ${errorMessage}`

    // Show error for longer
    setTimeout(() => {
      isProcessing.value = false
    }, 3000)
  }
}

// Simulate carving operations with proper async handling
const simulateCarving = async () => {
  if (!voxelGrid.value || !carvingEngine.value) {
    console.warn('Voxel grid or carving engine not initialized')
    return
  }

  if (isProcessing.value) return // Prevent multiple simultaneous operations

  isProcessing.value = true
  progressMessage.value = 'Processing toolpaths...'
  progressPercent.value = 0

  try {
    // Reset grid to original state
    voxelGrid.value.reset()
    carvingEngine.value.clearOperations()

    const pathProcessor = new VoxelPathProcessor(voxelResolution.value)
    const processedCommands = props.drawCommands.filter(cmd =>
      cmd.layer_name.toUpperCase() !== 'PANEL' && // Skip panel layer
      !cmd.layer_name.toUpperCase().startsWith('LMM') // Skip measurement layers
    )

    if (processedCommands.length === 0) {
      progressMessage.value = 'No valid operations found'
      setTimeout(() => {
        isProcessing.value = false
      }, 2000)
      return
    }

    progressPercent.value = 10
    progressMessage.value = `Processing ${processedCommands.length} operations...`
    await nextTick()

    // Process each draw command with yielding
    for (let i = 0; i < processedCommands.length; i++) {
      const command = processedCommands[i]

      // Yield to UI every few operations
      if (i % 5 === 0) {
        await new Promise(resolve => setTimeout(resolve, 1))
      }

      try {
        // Get or create tool for this layer
        const tool = voxelToolLibrary.createToolFromLayer(command.layer_name, 10) ||
                     voxelToolLibrary.getTool('endmill', defaultToolDiameter.value)

        // Convert command to toolpath
        const toolPaths = pathProcessor.convertDrawCommandsToToolPaths(
          [command],
          command.thickness ? Math.abs(command.thickness) : defaultCutDepth.value,
          tool
        )

        // Add carving operations
        for (const toolPath of toolPaths) {
          if (toolPath.points.length > 0) {
            carvingEngine.value.simulateToolSweep(
              tool,
              toolPath,
              0,
              'top' // Assume top face for now
            )
          }
        }
      } catch (error) {
        console.warn(`Failed to process command ${i}:`, error)
        // Continue with other operations
      }

      progressPercent.value = 10 + (i / processedCommands.length) * 70
      progressMessage.value = `Processing operation ${i + 1}/${processedCommands.length}`
    }

    progressMessage.value = 'Executing carving operations...'
    progressPercent.value = 80

    // Execute all operations
    carvingEngine.value.executeOperations((progress, operation) => {
      progressPercent.value = 80 + (progress / 100) * 20
      progressMessage.value = operation
    })

    progressPercent.value = 100
    progressMessage.value = 'Carving simulation complete!'

    // Update statistics
    updateStatistics()

    setTimeout(() => {
      isProcessing.value = false
    }, 1000)

  } catch (error) {
    console.error('Failed to simulate carving:', error)
    isProcessing.value = false
  }
}

// Reset simulation
const resetSimulation = () => {
  if (voxelGrid.value) {
    voxelGrid.value.reset()
    updateStatistics()
  }
}

// Update simulation statistics
const updateStatistics = () => {
  if (!voxelGrid.value) {
    simulationStats.value = null
    return
  }

  const dimensions = voxelGrid.value.getDimensions()
  const memoryStats = voxelGrid.value.getMemoryStats()
  
  let removedVolumeMM3 = 0
  if (carvingEngine.value) {
    const materialRemoval = carvingEngine.value.calculateMaterialRemoval()
    removedVolumeMM3 = materialRemoval.removedVolumeMM3
  }

  simulationStats.value = {
    gridDimensions: dimensions,
    totalVoxels: memoryStats.totalVoxels,
    removedVolumeMM3,
    memoryMB: memoryStats.memoryMB
  }
}

// Export functions
const exportSTL = async () => {
  if (!voxelGrid.value) return

  try {
    isProcessing.value = true
    progressMessage.value = 'Exporting STL...'
    progressPercent.value = 0

    await voxelExporter.exportSTL(voxelGrid.value, 'voxel_door_model.stl', true)

    progressPercent.value = 100
    progressMessage.value = 'STL exported successfully!'

    setTimeout(() => {
      isProcessing.value = false
    }, 1000)
  } catch (error) {
    console.error('STL export failed:', error)
    isProcessing.value = false
  }
}

const exportGLTF = async () => {
  if (!voxelGrid.value) return

  try {
    isProcessing.value = true
    progressMessage.value = 'Exporting GLTF...'
    progressPercent.value = 0

    await voxelExporter.exportGLTF(voxelGrid.value, 'voxel_door_model.gltf', false)

    progressPercent.value = 100
    progressMessage.value = 'GLTF exported successfully!'

    setTimeout(() => {
      isProcessing.value = false
    }, 1000)
  } catch (error) {
    console.error('GLTF export failed:', error)
    isProcessing.value = false
  }
}

// Update performance statistics
const updatePerformanceStats = (operationTime: number) => {
  const memoryInfo = voxelOptimizer.monitorMemoryUsage()
  const cacheStats = voxelOptimizer.getCacheStats()
  const qualitySettings = voxelOptimizer.getAdaptiveQualitySettings()

  // Calculate cache hit rate (simplified)
  const totalCacheSize = cacheStats.toolCacheSize + cacheStats.meshCacheSize + cacheStats.operationCacheSize
  const cacheHitRate = totalCacheSize > 0 ? Math.min(95, 60 + totalCacheSize * 2) : 0

  performanceStats.value = {
    cacheHitRate,
    lastOperationTime: operationTime,
    heapUsed: memoryInfo.heapUsed,
    qualityLevel: qualitySettings.voxelResolution === 0.5 ? 'High' :
                  qualitySettings.voxelResolution === 1.0 ? 'Medium' : 'Low'
  }
}

// Update memory warning based on current settings
const updateMemoryWarning = () => {
  const estimatedVoxels = Math.ceil(workpieceWidth.value / voxelResolution.value) *
                         Math.ceil(workpieceHeight.value / voxelResolution.value) *
                         Math.ceil(workpieceThickness.value / voxelResolution.value)

  const estimatedMemoryMB = estimatedVoxels / (1024 * 1024)

  if (estimatedMemoryMB > 200) {
    memoryWarning.value = `High memory usage: ${estimatedMemoryMB.toFixed(0)}MB. Consider increasing resolution.`
  } else if (estimatedMemoryMB > 100) {
    memoryWarning.value = `Moderate memory usage: ${estimatedMemoryMB.toFixed(0)}MB. May be slow.`
  } else if (estimatedVoxels > 50_000_000) {
    memoryWarning.value = `Large grid: ${(estimatedVoxels / 1_000_000).toFixed(1)}M voxels. Processing may take time.`
  } else {
    memoryWarning.value = ''
  }
}

// Initialize component (don't auto-generate grid to prevent freezing)
onMounted(() => {
  // Don't automatically generate grid - let user click "Generate Grid" button
  updateMemoryWarning()
  console.log('Voxel CAM Simulator initialized. Click "Generate Grid" to start.')
})
</script>

<style scoped>
.voxel-cam-simulator {
  display: flex;
  height: 100%;
  position: relative;
}

.control-panel {
  width: 300px;
  background: #f8f9fa;
  border-right: 1px solid #dee2e6;
  padding: 1rem;
  overflow-y: auto;
}

.control-section {
  margin-bottom: 1.5rem;
}

.control-section h3 {
  margin: 0 0 0.75rem 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: #495057;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.warning-message {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  color: #856404;
  padding: 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  margin-bottom: 0.75rem;
}

.control-row {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
  gap: 0.5rem;
}

.control-row label {
  flex: 1;
  font-size: 0.875rem;
  color: #495057;
}

.control-row input,
.control-row select {
  flex: 1;
  padding: 0.375rem 0.5rem;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  font-size: 0.875rem;
}

.primary-button {
  padding: 0.5rem 1rem;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.15s;
}

.primary-button:hover:not(:disabled) {
  background: #0056b3;
}

.primary-button:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.secondary-button {
  padding: 0.5rem 1rem;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.15s;
}

.secondary-button:hover:not(:disabled) {
  background: #545b62;
}

.export-button {
  padding: 0.5rem 1rem;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.15s;
}

.export-button:hover:not(:disabled) {
  background: #1e7e34;
}

.export-button:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.5rem;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background: white;
  border-radius: 0.25rem;
  border: 1px solid #e9ecef;
}

.stat-label {
  font-size: 0.75rem;
  color: #6c757d;
  font-weight: 500;
}

.stat-value {
  font-size: 0.75rem;
  color: #495057;
  font-weight: 600;
}

.viewer-container {
  flex: 1;
  position: relative;
}

.progress-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.progress-content {
  text-align: center;
  max-width: 300px;
}

.progress-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

.progress-content h3 {
  margin: 0 0 1rem 0;
  color: #495057;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: #007bff;
  transition: width 0.3s ease;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
