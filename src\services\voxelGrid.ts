import ndarray from 'ndarray'
import type { VoxelGridConfig, VoxelCoordinate, WorldCoordinate } from '@/types'

/**
 * VoxelGrid class for high-resolution voxel-based CAM simulation
 * Manages a 3D grid of voxels representing the workpiece material
 */
export class VoxelGrid {
  private grid: ndarray.NdArray<Uint8Array>
  private config: VoxelGridConfig
  private dimensions: [number, number, number]

  constructor(config: VoxelGridConfig) {
    this.config = config

    // Validate configuration
    if (config.width <= 0 || config.height <= 0 || config.thickness <= 0) {
      throw new Error('Invalid dimensions: all values must be positive')
    }

    if (config.resolution <= 0 || config.resolution > 10) {
      throw new Error('Invalid resolution: must be between 0.1 and 10.0mm')
    }

    // Calculate grid dimensions in voxels
    this.dimensions = [
      Math.ceil(config.width / config.resolution),
      Math.ceil(config.height / config.resolution),
      Math.ceil(config.thickness / config.resolution)
    ]

    // Safety check for memory usage
    const totalVoxels = this.dimensions[0] * this.dimensions[1] * this.dimensions[2]
    const memoryMB = totalVoxels / (1024 * 1024)

    if (totalVoxels > 100_000_000) { // 100M voxels limit
      throw new Error(`Grid too large: ${totalVoxels.toLocaleString()} voxels (${memoryMB.toFixed(1)}MB). Please increase resolution or reduce dimensions.`)
    }

    if (memoryMB > 200) { // 200MB limit
      console.warn(`Large memory allocation: ${memoryMB.toFixed(1)}MB for ${totalVoxels.toLocaleString()} voxels`)
    }

    try {
      // Create the 3D array - 1 = material present, 0 = material removed
      const data = new Uint8Array(totalVoxels)
      data.fill(1) // Initialize with material present

      this.grid = ndarray(data, this.dimensions)

      console.log(`VoxelGrid created: ${this.dimensions[0]}x${this.dimensions[1]}x${this.dimensions[2]} voxels`)
      console.log(`Resolution: ${config.resolution}mm/voxel`)
      console.log(`Physical size: ${config.width}x${config.height}x${config.thickness}mm`)
      console.log(`Memory usage: ${memoryMB.toFixed(2)}MB`)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown allocation error'
      throw new Error(`Failed to allocate voxel memory: ${errorMessage}. Try increasing resolution or reducing dimensions.`)
    }
  }

  /**
   * Convert world coordinates (mm) to voxel indices
   */
  worldToVoxel(world: WorldCoordinate): VoxelCoordinate {
    return {
      x: Math.floor(world.x / this.config.resolution),
      y: Math.floor(world.y / this.config.resolution),
      z: Math.floor(world.z / this.config.resolution)
    }
  }

  /**
   * Convert voxel indices to world coordinates (mm)
   */
  voxelToWorld(voxel: VoxelCoordinate): WorldCoordinate {
    return {
      x: voxel.x * this.config.resolution,
      y: voxel.y * this.config.resolution,
      z: voxel.z * this.config.resolution
    }
  }

  /**
   * Check if voxel coordinates are within grid bounds
   */
  isValidVoxel(voxel: VoxelCoordinate): boolean {
    return (
      voxel.x >= 0 && voxel.x < this.dimensions[0] &&
      voxel.y >= 0 && voxel.y < this.dimensions[1] &&
      voxel.z >= 0 && voxel.z < this.dimensions[2]
    )
  }

  /**
   * Get voxel value (1 = material, 0 = empty)
   */
  getVoxel(voxel: VoxelCoordinate): number {
    if (!this.isValidVoxel(voxel)) return 0
    return this.grid.get(voxel.x, voxel.y, voxel.z)
  }

  /**
   * Set voxel value (1 = material, 0 = empty)
   */
  setVoxel(voxel: VoxelCoordinate, value: number): void {
    if (!this.isValidVoxel(voxel)) return
    this.grid.set(voxel.x, voxel.y, voxel.z, value)
  }

  /**
   * Remove material at voxel position (set to 0)
   */
  removeVoxel(voxel: VoxelCoordinate): void {
    this.setVoxel(voxel, 0)
  }

  /**
   * Remove material at world position
   */
  removeAtWorld(world: WorldCoordinate): void {
    const voxel = this.worldToVoxel(world)
    this.removeVoxel(voxel)
  }

  /**
   * Apply tool shape at given position
   * @param toolOffsets Array of relative voxel offsets that define the tool shape
   * @param position World position where tool tip is located
   */
  applyTool(toolOffsets: VoxelCoordinate[], position: WorldCoordinate): void {
    const centerVoxel = this.worldToVoxel(position)
    
    for (const offset of toolOffsets) {
      const targetVoxel: VoxelCoordinate = {
        x: centerVoxel.x + offset.x,
        y: centerVoxel.y + offset.y,
        z: centerVoxel.z + offset.z
      }
      this.removeVoxel(targetVoxel)
    }
  }

  /**
   * Get grid dimensions
   */
  getDimensions(): [number, number, number] {
    return [...this.dimensions]
  }

  /**
   * Get grid configuration
   */
  getConfig(): VoxelGridConfig {
    return { ...this.config }
  }

  /**
   * Get the raw ndarray for mesh generation
   */
  getRawGrid(): ndarray.NdArray<Uint8Array> {
    return this.grid
  }

  /**
   * Reset grid to initial state (all material present)
   */
  reset(): void {
    const data = this.grid.data as Uint8Array
    data.fill(1)
  }

  /**
   * Get memory usage statistics
   */
  getMemoryStats(): { totalVoxels: number; memoryMB: number } {
    const totalVoxels = this.dimensions[0] * this.dimensions[1] * this.dimensions[2]
    const memoryMB = (totalVoxels * 1) / (1024 * 1024) // 1 byte per voxel
    return { totalVoxels, memoryMB }
  }

  /**
   * Create a copy of the current grid state
   */
  clone(): VoxelGrid {
    const newGrid = new VoxelGrid(this.config)
    const sourceData = this.grid.data as Uint8Array
    const targetData = newGrid.grid.data as Uint8Array
    targetData.set(sourceData)
    return newGrid
  }

  /**
   * Get a slice of the grid at a specific Z level for debugging
   */
  getZSlice(z: number): number[][] {
    const slice: number[][] = []
    for (let y = 0; y < this.dimensions[1]; y++) {
      const row: number[] = []
      for (let x = 0; x < this.dimensions[0]; x++) {
        row.push(this.getVoxel({ x, y, z }))
      }
      slice.push(row)
    }
    return slice
  }
}

/**
 * Factory function to create a voxel grid with default door dimensions
 */
export function createDoorVoxelGrid(
  width: number = 600,
  height: number = 800, 
  thickness: number = 18,
  resolution: number = 0.5
): VoxelGrid {
  return new VoxelGrid({ width, height, thickness, resolution })
}
