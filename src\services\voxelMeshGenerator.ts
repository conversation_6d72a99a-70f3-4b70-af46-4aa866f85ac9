import * as THREE from 'three'
import greedyMesher from 'greedy-mesher'
import isosurface from 'isosurface'
import type { VoxelGrid } from './voxelGrid'

/**
 * VoxelMeshGenerator converts voxel grids to Three.js meshes
 */
export class VoxelMeshGenerator {
  private resolution: number

  constructor(resolution: number = 0.5) {
    this.resolution = resolution
  }

  /**
   * Generate mesh using greedy meshing algorithm (faster, blockier)
   */
  generateMeshGreedy(grid: VoxelGrid): THREE.Mesh {
    const dimensions = grid.getDimensions()
    const rawGrid = grid.getRawGrid()
    
    // Convert ndarray to format expected by greedy-mesher
    const voxelData: number[][][] = []
    
    for (let x = 0; x < dimensions[0]; x++) {
      voxelData[x] = []
      for (let y = 0; y < dimensions[1]; y++) {
        voxelData[x][y] = []
        for (let z = 0; z < dimensions[2]; z++) {
          voxelData[x][y][z] = rawGrid.get(x, y, z)
        }
      }
    }

    // Generate mesh using greedy mesher
    const meshData = greedyMesher(voxelData)
    
    // Convert to Three.js geometry
    const geometry = new THREE.BufferGeometry()
    
    if (meshData.vertices.length > 0) {
      // Scale vertices by resolution
      const scaledVertices = new Float32Array(meshData.vertices.length)
      for (let i = 0; i < meshData.vertices.length; i++) {
        scaledVertices[i] = meshData.vertices[i] * this.resolution
      }
      
      geometry.setAttribute('position', new THREE.BufferAttribute(scaledVertices, 3))
      geometry.setIndex(meshData.faces)
      geometry.computeVertexNormals()
    }

    // Create material
    const material = new THREE.MeshLambertMaterial({
      color: 0xd4a574, // Wood color
      side: THREE.DoubleSide
    })

    return new THREE.Mesh(geometry, material)
  }

  /**
   * Generate mesh using marching cubes algorithm (smoother, more detailed)
   */
  generateMeshMarching(grid: VoxelGrid): THREE.Mesh {
    const dimensions = grid.getDimensions()
    const rawGrid = grid.getRawGrid()
    
    // Convert ndarray to format expected by isosurface
    const voxelFunction = (x: number, y: number, z: number): number => {
      const vx = Math.floor(x)
      const vy = Math.floor(y)
      const vz = Math.floor(z)
      
      if (vx < 0 || vx >= dimensions[0] || 
          vy < 0 || vy >= dimensions[1] || 
          vz < 0 || vz >= dimensions[2]) {
        return 0
      }
      
      return rawGrid.get(vx, vy, vz)
    }

    // Generate isosurface
    const meshData = isosurface.marchingCubes(
      [dimensions[0], dimensions[1], dimensions[2]],
      voxelFunction,
      0.5 // isovalue
    )

    // Convert to Three.js geometry
    const geometry = new THREE.BufferGeometry()
    
    if (meshData.positions.length > 0) {
      // Scale vertices by resolution
      const scaledVertices = new Float32Array(meshData.positions.length)
      for (let i = 0; i < meshData.positions.length; i++) {
        scaledVertices[i] = meshData.positions[i] * this.resolution
      }
      
      geometry.setAttribute('position', new THREE.BufferAttribute(scaledVertices, 3))
      
      if (meshData.cells && meshData.cells.length > 0) {
        const indices = new Uint32Array(meshData.cells.flat())
        geometry.setIndex(new THREE.BufferAttribute(indices, 1))
      }
      
      geometry.computeVertexNormals()
    }

    // Create material
    const material = new THREE.MeshLambertMaterial({
      color: 0xd4a574, // Wood color
      side: THREE.DoubleSide
    })

    return new THREE.Mesh(geometry, material)
  }

  /**
   * Generate mesh with custom material options
   */
  generateMeshWithMaterial(
    grid: VoxelGrid,
    materialOptions: {
      color?: number
      wireframe?: boolean
      transparent?: boolean
      opacity?: number
      roughness?: number
      metalness?: number
    } = {},
    algorithm: 'greedy' | 'marching' = 'greedy'
  ): THREE.Mesh {
    const mesh = algorithm === 'greedy' 
      ? this.generateMeshGreedy(grid)
      : this.generateMeshMarching(grid)

    // Apply custom material
    const material = new THREE.MeshStandardMaterial({
      color: materialOptions.color || 0xd4a574,
      wireframe: materialOptions.wireframe || false,
      transparent: materialOptions.transparent || false,
      opacity: materialOptions.opacity || 1.0,
      roughness: materialOptions.roughness || 0.8,
      metalness: materialOptions.metalness || 0.1,
      side: THREE.DoubleSide
    })

    mesh.material = material
    return mesh
  }

  /**
   * Generate wireframe mesh for debugging
   */
  generateWireframeMesh(grid: VoxelGrid): THREE.Mesh {
    return this.generateMeshWithMaterial(grid, {
      wireframe: true,
      color: 0x00ff00
    })
  }

  /**
   * Generate mesh with texture mapping
   */
  generateTexturedMesh(
    grid: VoxelGrid,
    textureUrl?: string,
    algorithm: 'greedy' | 'marching' = 'greedy'
  ): THREE.Mesh {
    const mesh = algorithm === 'greedy' 
      ? this.generateMeshGreedy(grid)
      : this.generateMeshMarching(grid)

    if (textureUrl) {
      const textureLoader = new THREE.TextureLoader()
      const texture = textureLoader.load(textureUrl)
      texture.wrapS = THREE.RepeatWrapping
      texture.wrapT = THREE.RepeatWrapping
      texture.repeat.set(4, 4)

      const material = new THREE.MeshLambertMaterial({
        map: texture,
        side: THREE.DoubleSide
      })

      mesh.material = material
    }

    return mesh
  }

  /**
   * Generate multiple LOD levels for performance optimization
   */
  generateLODMesh(grid: VoxelGrid): THREE.LOD {
    const lod = new THREE.LOD()

    // High detail (original resolution)
    const highDetail = this.generateMeshMarching(grid)
    lod.addLevel(highDetail, 0)

    // Medium detail (half resolution)
    const mediumGrid = this.downsampleGrid(grid, 2)
    const mediumDetail = this.generateMeshGreedy(mediumGrid)
    lod.addLevel(mediumDetail, 50)

    // Low detail (quarter resolution)
    const lowGrid = this.downsampleGrid(grid, 4)
    const lowDetail = this.generateMeshGreedy(lowGrid)
    lod.addLevel(lowDetail, 200)

    return lod
  }

  /**
   * Downsample grid for LOD generation
   */
  private downsampleGrid(grid: VoxelGrid, factor: number): VoxelGrid {
    const config = grid.getConfig()
    const newConfig = {
      ...config,
      resolution: config.resolution * factor
    }

    // This is a simplified downsampling - in practice you'd want more sophisticated filtering
    const newGrid = new (grid.constructor as any)(newConfig)
    const oldDims = grid.getDimensions()
    const newDims = newGrid.getDimensions()

    for (let x = 0; x < newDims[0]; x++) {
      for (let y = 0; y < newDims[1]; y++) {
        for (let z = 0; z < newDims[2]; z++) {
          // Sample from original grid
          const oldX = Math.min(x * factor, oldDims[0] - 1)
          const oldY = Math.min(y * factor, oldDims[1] - 1)
          const oldZ = Math.min(z * factor, oldDims[2] - 1)
          
          const value = grid.getVoxel({ x: oldX, y: oldY, z: oldZ })
          newGrid.setVoxel({ x, y, z }, value)
        }
      }
    }

    return newGrid
  }

  /**
   * Generate mesh with vertex colors based on depth
   */
  generateDepthColoredMesh(grid: VoxelGrid): THREE.Mesh {
    const mesh = this.generateMeshGreedy(grid)
    const geometry = mesh.geometry as THREE.BufferGeometry
    const positions = geometry.getAttribute('position')
    
    if (positions) {
      const colors = new Float32Array(positions.count * 3)
      const config = grid.getConfig()
      
      for (let i = 0; i < positions.count; i++) {
        const z = positions.getZ(i)
        const normalizedZ = z / (config.thickness * this.resolution)
        
        // Color gradient from blue (bottom) to red (top)
        colors[i * 3] = normalizedZ     // R
        colors[i * 3 + 1] = 0.2         // G
        colors[i * 3 + 2] = 1 - normalizedZ // B
      }
      
      geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3))
      
      const material = new THREE.MeshLambertMaterial({
        vertexColors: true,
        side: THREE.DoubleSide
      })
      
      mesh.material = material
    }

    return mesh
  }

  /**
   * Get mesh generation statistics
   */
  getMeshStats(mesh: THREE.Mesh): {
    vertices: number
    faces: number
    memoryMB: number
  } {
    const geometry = mesh.geometry as THREE.BufferGeometry
    const positions = geometry.getAttribute('position')
    const indices = geometry.getIndex()
    
    const vertices = positions ? positions.count : 0
    const faces = indices ? indices.count / 3 : 0
    
    // Rough memory estimate
    const positionMemory = vertices * 3 * 4 // 3 floats * 4 bytes
    const indexMemory = faces * 3 * 4 // 3 indices * 4 bytes
    const memoryMB = (positionMemory + indexMemory) / (1024 * 1024)
    
    return { vertices, faces, memoryMB }
  }
}
