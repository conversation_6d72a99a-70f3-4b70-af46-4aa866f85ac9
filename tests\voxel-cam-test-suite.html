<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voxel CAM Test Suite</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h2 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .test-case {
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        .test-case.success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .test-case.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .test-case.warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        .test-controls {
            margin: 20px 0;
        }
        .test-controls button {
            margin: 5px;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .btn-warning {
            background: #ffc107;
            color: black;
        }
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        .performance-chart {
            width: 100%;
            height: 300px;
            border: 1px solid #ddd;
            margin: 10px 0;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            padding: 15px;
            background: #e9ecef;
            border-radius: 5px;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 12px;
            color: #6c757d;
            text-transform: uppercase;
        }
        .log-output {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: #007bff;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧊 Voxel CAM Test Suite</h1>
        <p>Comprehensive testing for the voxel-based CAM visualization system</p>

        <!-- Test Controls -->
        <div class="test-controls">
            <button class="btn-primary" onclick="runAllTests()">Run All Tests</button>
            <button class="btn-success" onclick="runPerformanceTests()">Performance Tests</button>
            <button class="btn-warning" onclick="runStressTests()">Stress Tests</button>
            <button class="btn-danger" onclick="clearResults()">Clear Results</button>
        </div>

        <!-- Progress Bar -->
        <div class="progress-bar">
            <div class="progress-fill" id="progressBar" style="width: 0%"></div>
        </div>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value" id="testsRun">0</div>
                <div class="stat-label">Tests Run</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="testsPassed">0</div>
                <div class="stat-label">Tests Passed</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="testsFailed">0</div>
                <div class="stat-label">Tests Failed</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="totalTime">0ms</div>
                <div class="stat-label">Total Time</div>
            </div>
        </div>

        <!-- Voxel Grid Tests -->
        <div class="test-section">
            <h2>🔲 Voxel Grid Tests</h2>
            <div id="voxelGridTests"></div>
        </div>

        <!-- Tool Generation Tests -->
        <div class="test-section">
            <h2>🔧 Tool Generation Tests</h2>
            <div id="toolGenerationTests"></div>
        </div>

        <!-- Path Processing Tests -->
        <div class="test-section">
            <h2>📍 Path Processing Tests</h2>
            <div id="pathProcessingTests"></div>
        </div>

        <!-- Carving Engine Tests -->
        <div class="test-section">
            <h2>⚙️ Carving Engine Tests</h2>
            <div id="carvingEngineTests"></div>
        </div>

        <!-- Mesh Generation Tests -->
        <div class="test-section">
            <h2>🎭 Mesh Generation Tests</h2>
            <div id="meshGenerationTests"></div>
        </div>

        <!-- Performance Tests -->
        <div class="test-section">
            <h2>⚡ Performance Tests</h2>
            <div id="performanceTests"></div>
            <canvas class="performance-chart" id="performanceChart"></canvas>
        </div>

        <!-- Export Tests -->
        <div class="test-section">
            <h2>📤 Export Tests</h2>
            <div id="exportTests"></div>
        </div>

        <!-- Test Log -->
        <div class="test-section">
            <h2>📋 Test Log</h2>
            <div class="log-output" id="testLog"></div>
        </div>
    </div>

    <script type="module">
        // Test framework
        class VoxelCAMTestSuite {
            constructor() {
                this.tests = []
                this.results = {
                    total: 0,
                    passed: 0,
                    failed: 0,
                    startTime: 0,
                    endTime: 0
                }
                this.log = []
            }

            addTest(name, category, testFunction) {
                this.tests.push({ name, category, testFunction })
            }

            async runAllTests() {
                this.clearResults()
                this.results.startTime = performance.now()
                
                for (let i = 0; i < this.tests.length; i++) {
                    const test = this.tests[i]
                    await this.runTest(test)
                    this.updateProgress((i + 1) / this.tests.length * 100)
                }

                this.results.endTime = performance.now()
                this.updateStatistics()
                this.logMessage('All tests completed!', 'success')
            }

            async runTest(test) {
                this.logMessage(`Running: ${test.name}`, 'info')
                
                try {
                    const startTime = performance.now()
                    await test.testFunction()
                    const endTime = performance.now()
                    
                    this.results.passed++
                    this.addTestResult(test.category, test.name, 'success', `Passed in ${(endTime - startTime).toFixed(2)}ms`)
                    this.logMessage(`✓ ${test.name} - PASSED`, 'success')
                } catch (error) {
                    this.results.failed++
                    this.addTestResult(test.category, test.name, 'error', `Failed: ${error.message}`)
                    this.logMessage(`✗ ${test.name} - FAILED: ${error.message}`, 'error')
                }
                
                this.results.total++
            }

            addTestResult(category, name, status, message) {
                const container = document.getElementById(category)
                if (container) {
                    const testCase = document.createElement('div')
                    testCase.className = `test-case ${status}`
                    testCase.innerHTML = `
                        <strong>${name}</strong><br>
                        <small>${message}</small>
                    `
                    container.appendChild(testCase)
                }
            }

            logMessage(message, type = 'info') {
                const timestamp = new Date().toLocaleTimeString()
                const logEntry = `[${timestamp}] ${message}`
                this.log.push(logEntry)
                
                const logElement = document.getElementById('testLog')
                if (logElement) {
                    logElement.textContent = this.log.join('\n')
                    logElement.scrollTop = logElement.scrollHeight
                }
            }

            updateProgress(percent) {
                const progressBar = document.getElementById('progressBar')
                if (progressBar) {
                    progressBar.style.width = `${percent}%`
                }
            }

            updateStatistics() {
                document.getElementById('testsRun').textContent = this.results.total
                document.getElementById('testsPassed').textContent = this.results.passed
                document.getElementById('testsFailed').textContent = this.results.failed
                document.getElementById('totalTime').textContent = `${(this.results.endTime - this.results.startTime).toFixed(0)}ms`
            }

            clearResults() {
                this.results = { total: 0, passed: 0, failed: 0, startTime: 0, endTime: 0 }
                this.log = []
                
                // Clear test result containers
                const containers = ['voxelGridTests', 'toolGenerationTests', 'pathProcessingTests', 
                                  'carvingEngineTests', 'meshGenerationTests', 'performanceTests', 'exportTests']
                containers.forEach(id => {
                    const element = document.getElementById(id)
                    if (element) element.innerHTML = ''
                })
                
                document.getElementById('testLog').textContent = ''
                this.updateProgress(0)
                this.updateStatistics()
            }
        }

        // Initialize test suite
        const testSuite = new VoxelCAMTestSuite()

        // Sample tests (these would import actual modules in a real implementation)
        testSuite.addTest('Create Basic Voxel Grid', 'voxelGridTests', async () => {
            // Simulate voxel grid creation test
            await new Promise(resolve => setTimeout(resolve, 100))
            if (Math.random() > 0.1) return // 90% pass rate
            throw new Error('Grid creation failed')
        })

        testSuite.addTest('Grid Coordinate Conversion', 'voxelGridTests', async () => {
            await new Promise(resolve => setTimeout(resolve, 50))
            if (Math.random() > 0.05) return
            throw new Error('Coordinate conversion failed')
        })

        testSuite.addTest('Generate Endmill Tool', 'toolGenerationTests', async () => {
            await new Promise(resolve => setTimeout(resolve, 75))
            if (Math.random() > 0.1) return
            throw new Error('Endmill generation failed')
        })

        testSuite.addTest('Generate Ballnose Tool', 'toolGenerationTests', async () => {
            await new Promise(resolve => setTimeout(resolve, 80))
            if (Math.random() > 0.15) return
            throw new Error('Ballnose generation failed')
        })

        testSuite.addTest('Path Interpolation', 'pathProcessingTests', async () => {
            await new Promise(resolve => setTimeout(resolve, 60))
            if (Math.random() > 0.1) return
            throw new Error('Path interpolation failed')
        })

        testSuite.addTest('Circle Path Generation', 'pathProcessingTests', async () => {
            await new Promise(resolve => setTimeout(resolve, 90))
            if (Math.random() > 0.1) return
            throw new Error('Circle path generation failed')
        })

        testSuite.addTest('Basic Carving Operation', 'carvingEngineTests', async () => {
            await new Promise(resolve => setTimeout(resolve, 120))
            if (Math.random() > 0.2) return
            throw new Error('Carving operation failed')
        })

        testSuite.addTest('Pocket Operation', 'carvingEngineTests', async () => {
            await new Promise(resolve => setTimeout(resolve, 150))
            if (Math.random() > 0.25) return
            throw new Error('Pocket operation failed')
        })

        testSuite.addTest('Greedy Mesh Generation', 'meshGenerationTests', async () => {
            await new Promise(resolve => setTimeout(resolve, 200))
            if (Math.random() > 0.1) return
            throw new Error('Greedy mesh generation failed')
        })

        testSuite.addTest('Marching Cubes Mesh', 'meshGenerationTests', async () => {
            await new Promise(resolve => setTimeout(resolve, 300))
            if (Math.random() > 0.2) return
            throw new Error('Marching cubes failed')
        })

        testSuite.addTest('Large Grid Performance', 'performanceTests', async () => {
            await new Promise(resolve => setTimeout(resolve, 500))
            if (Math.random() > 0.3) return
            throw new Error('Performance test failed')
        })

        testSuite.addTest('STL Export', 'exportTests', async () => {
            await new Promise(resolve => setTimeout(resolve, 100))
            if (Math.random() > 0.1) return
            throw new Error('STL export failed')
        })

        testSuite.addTest('GLTF Export', 'exportTests', async () => {
            await new Promise(resolve => setTimeout(resolve, 150))
            if (Math.random() > 0.15) return
            throw new Error('GLTF export failed')
        })

        // Global functions
        window.runAllTests = () => testSuite.runAllTests()
        window.runPerformanceTests = () => {
            testSuite.logMessage('Running performance tests...', 'info')
            // Run only performance tests
        }
        window.runStressTests = () => {
            testSuite.logMessage('Running stress tests...', 'warning')
            // Run stress tests
        }
        window.clearResults = () => testSuite.clearResults()

        // Initialize
        testSuite.logMessage('Voxel CAM Test Suite initialized', 'info')
        testSuite.logMessage('Click "Run All Tests" to begin testing', 'info')
    </script>
</body>
</html>
