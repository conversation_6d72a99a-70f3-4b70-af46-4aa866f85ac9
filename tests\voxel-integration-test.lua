-- Voxel CAM Integration Test Script
-- This script creates a comprehensive door model to test the voxel CAM system

-- Material thickness
materialThickness = 18

function modelMain()
    -- Initialize ADekoLib
    G = AdekoLib
    
    -- Set up basic parameters
    X = 600  -- Door width
    Y = 800  -- Door height
    
    print("=== Voxel CAM Integration Test ===")
    print("Creating comprehensive door model for voxel simulation")
    
    -- Create door panel (PANEL layer - this will be the base door body)
    G.setFace("top")
    G.setThickness(-materialThickness)
    G.makePartShape()
    print("✓ Door panel created (600x800x18mm)")
    
    -- TOP SURFACE OPERATIONS
    print("\n--- Top Surface Operations ---")
    
    -- 1. Groove operation with 6mm endmill
    G.setLayer("H_Freze6mm")
    G.setThickness(-3)
    G.rectangle({50, 50}, {550, 100})
    print("✓ Rectangular groove (6mm endmill, 3mm deep)")
    
    -- 2. Circular pocket with 8mm endmill
    G.setLayer("H_Freze8mm")
    G.setThickness(-5)
    <PERSON>.circle({150, 200}, 40)
    print("✓ Circular pocket (8mm endmill, 5mm deep)")
    
    -- 3. V-carve operation
    G.setLayer("K_AciliV120")
    G.setThickness(-2)
    G.polyline({300, 150}, {450, 150}, {450, 250}, {300, 250}, {300, 150})
    print("✓ V-carve rectangle (V120 bit, 2mm deep)")
    
    -- 4. Complex curved path
    G.setLayer("H_Freze4mm")
    G.setThickness(-4)
    -- Create a curved decorative element
    local centerX, centerY = 400, 400
    local radius = 80
    local points = {}
    for i = 0, 32 do
        local angle = (i / 32) * 2 * math.pi
        local x = centerX + radius * math.cos(angle)
        local y = centerY + radius * math.sin(angle)
        table.insert(points, {x, y})
    end
    G.polyline(table.unpack(points))
    print("✓ Circular decorative groove (4mm endmill, 4mm deep)")
    
    -- 5. Small detail holes
    G.setLayer("DRILL3mm")
    G.setThickness(-8)
    for i = 1, 5 do
        local x = 100 + (i - 1) * 80
        G.circle({x, 600}, 1.5)
    end
    print("✓ Detail holes (3mm drill, 8mm deep)")
    
    -- 6. Ballnose finishing operation
    G.setLayer("K_Ballnose6mm")
    G.setThickness(-1)
    G.rectangle({200, 500}, {400, 600})
    print("✓ Ballnose finish area (6mm ballnose, 1mm deep)")
    
    -- BOTTOM SURFACE OPERATIONS
    G.setFace("bottom")
    print("\n--- Bottom Surface Operations ---")
    
    -- 7. Bottom face cutting operation
    G.setLayer("H_Freze10mm_SF")
    G.setThickness(-6)
    G.rectangle({100, 150}, {500, 250})
    print("✓ Bottom rectangular cut (10mm endmill, 6mm deep)")
    
    -- 8. Bottom face drilling
    G.setLayer("DRILL8mm_SF")
    G.setThickness(-10)
    G.circle({150, 400}, 4)
    G.circle({450, 400}, 4)
    print("✓ Bottom mounting holes (8mm drill, 10mm deep)")
    
    -- 9. Chamfer operation on bottom
    G.setLayer("K_AciliV90_SF")
    G.setThickness(-1.5)
    G.polyline({250, 600}, {350, 600}, {350, 700}, {250, 700}, {250, 600})
    print("✓ Bottom chamfer (V90 bit, 1.5mm deep)")
    
    -- 10. Complex pocket on bottom
    G.setLayer("H_Freze12mm_SF")
    G.setThickness(-8)
    -- Create an oval-like shape
    local ovalPoints = {}
    for i = 0, 24 do
        local angle = (i / 24) * 2 * math.pi
        local x = 300 + 60 * math.cos(angle)
        local y = 500 + 30 * math.sin(angle)
        table.insert(ovalPoints, {x, y})
    end
    G.polyline(table.unpack(ovalPoints))
    print("✓ Oval pocket on bottom (12mm endmill, 8mm deep)")
    
    print("\n=== Test Model Complete ===")
    print("Total operations: 10")
    print("Tool types used: Endmill (4-12mm), V-bit (90°, 120°), Ballnose (6mm), Drill (3mm, 8mm)")
    print("Depth range: 1-10mm")
    print("Both top and bottom face operations included")
    print("\nSwitch to 'Voxel CAM' tab in visualization to see 3D simulation")
    print("Expected processing time: 30-60 seconds depending on resolution")
    
    -- Performance recommendations
    print("\n--- Performance Recommendations ---")
    print("• Start with 1.0mm resolution for faster preview")
    print("• Use 0.5mm resolution for detailed visualization")
    print("• Use 0.25mm resolution only for final high-quality output")
    print("• Greedy mesher is faster, Marching Cubes is smoother")
    
    return true
end

-- Load ADekoDebugMode to execute the model
require "ADekoDebugMode"
