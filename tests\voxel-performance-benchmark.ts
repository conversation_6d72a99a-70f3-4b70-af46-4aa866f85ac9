import { VoxelGrid, createDoorVoxelGrid } from '../src/services/voxelGrid'
import { VoxelCarvingEngine } from '../src/services/voxelCarvingEngine'
import { VoxelMeshGenerator } from '../src/services/voxelMeshGenerator'
import { voxelToolLibrary } from '../src/services/voxelTools'
import { voxelOptimizer } from '../src/services/voxelPerformanceOptimizer'
import type { DrawCommand } from '../src/types'

/**
 * Performance benchmark suite for voxel CAM operations
 */
export class VoxelPerformanceBenchmark {
  private results: Map<string, BenchmarkResult> = new Map()

  /**
   * Run all benchmarks
   */
  async runAllBenchmarks(): Promise<BenchmarkReport> {
    console.log('🚀 Starting Voxel CAM Performance Benchmarks...')
    
    await this.benchmarkVoxelGridCreation()
    await this.benchmarkToolGeneration()
    await this.benchmarkCarvingOperations()
    await this.benchmarkMeshGeneration()
    await this.benchmarkMemoryUsage()
    await this.benchmarkScalability()

    return this.generateReport()
  }

  /**
   * Benchmark voxel grid creation with different resolutions
   */
  async benchmarkVoxelGridCreation(): Promise<void> {
    console.log('📊 Benchmarking voxel grid creation...')
    
    const resolutions = [0.25, 0.5, 1.0, 2.0]
    const dimensions = { width: 600, height: 800, thickness: 18 }

    for (const resolution of resolutions) {
      const result = await this.measurePerformance(
        `Grid Creation (${resolution}mm)`,
        () => {
          return createDoorVoxelGrid(
            dimensions.width,
            dimensions.height,
            dimensions.thickness,
            resolution
          )
        }
      )

      const grid = result.result as VoxelGrid
      const memoryStats = grid.getMemoryStats()
      
      result.metadata = {
        resolution,
        dimensions: grid.getDimensions(),
        totalVoxels: memoryStats.totalVoxels,
        memoryMB: memoryStats.memoryMB
      }

      this.results.set(`grid_creation_${resolution}`, result)
    }
  }

  /**
   * Benchmark tool generation
   */
  async benchmarkToolGeneration(): Promise<void> {
    console.log('🔧 Benchmarking tool generation...')
    
    const toolConfigs = [
      { type: 'endmill' as const, diameter: 6, height: 10 },
      { type: 'endmill' as const, diameter: 12, height: 15 },
      { type: 'ballnose' as const, diameter: 8, height: 12 },
      { type: 'chamfer' as const, diameter: 10, height: 10 }
    ]

    for (const config of toolConfigs) {
      const result = await this.measurePerformance(
        `Tool Generation (${config.type} ${config.diameter}mm)`,
        () => {
          return voxelToolLibrary.getTool(config.type, config.diameter, config.height)
        }
      )

      const tool = result.result
      result.metadata = {
        toolType: config.type,
        diameter: config.diameter,
        voxelCount: tool.voxelOffsets?.length || 0
      }

      this.results.set(`tool_${config.type}_${config.diameter}`, result)
    }
  }

  /**
   * Benchmark carving operations
   */
  async benchmarkCarvingOperations(): Promise<void> {
    console.log('⚙️ Benchmarking carving operations...')
    
    const grid = createDoorVoxelGrid(600, 800, 18, 1.0) // Use 1mm resolution for speed
    const engine = new VoxelCarvingEngine(grid)
    const tool = voxelToolLibrary.getTool('endmill', 6)

    // Test different operation types
    const operations = [
      {
        name: 'Simple Line',
        points: [{ x: 100, y: 100, z: 0 }, { x: 200, y: 100, z: 0 }]
      },
      {
        name: 'Circle Path',
        points: this.generateCirclePoints({ x: 300, y: 300, z: 0 }, 50, 32)
      },
      {
        name: 'Complex Polyline',
        points: this.generateComplexPath(100)
      }
    ]

    for (const operation of operations) {
      const result = await this.measurePerformance(
        `Carving (${operation.name})`,
        () => {
          const toolPath = {
            points: operation.points,
            depth: 5,
            toolType: 'endmill' as const,
            toolDiameter: 6
          }
          engine.simulateToolSweep(tool, toolPath)
        }
      )

      result.metadata = {
        operationType: operation.name,
        pointCount: operation.points.length,
        materialRemoval: engine.calculateMaterialRemoval()
      }

      this.results.set(`carving_${operation.name.toLowerCase().replace(' ', '_')}`, result)
      
      // Reset grid for next test
      grid.reset()
    }
  }

  /**
   * Benchmark mesh generation
   */
  async benchmarkMeshGeneration(): Promise<void> {
    console.log('🎭 Benchmarking mesh generation...')
    
    // Create a grid with some carved material
    const grid = createDoorVoxelGrid(400, 400, 18, 1.0)
    const engine = new VoxelCarvingEngine(grid)
    const tool = voxelToolLibrary.getTool('endmill', 8)
    
    // Add some carving operations
    const toolPath = {
      points: this.generateCirclePoints({ x: 200, y: 200, z: 0 }, 80, 64),
      depth: 8,
      toolType: 'endmill' as const,
      toolDiameter: 8
    }
    engine.simulateToolSweep(tool, toolPath)

    const meshGenerator = new VoxelMeshGenerator(1.0)
    
    // Test different mesh generation algorithms
    const algorithms = ['greedy', 'marching'] as const

    for (const algorithm of algorithms) {
      const result = await this.measurePerformance(
        `Mesh Generation (${algorithm})`,
        () => {
          return algorithm === 'greedy' 
            ? meshGenerator.generateMeshGreedy(grid)
            : meshGenerator.generateMeshMarching(grid)
        }
      )

      const mesh = result.result
      const stats = meshGenerator.getMeshStats(mesh)
      
      result.metadata = {
        algorithm,
        vertices: stats.vertices,
        faces: stats.faces,
        memoryMB: stats.memoryMB
      }

      this.results.set(`mesh_${algorithm}`, result)
      
      // Cleanup
      mesh.geometry.dispose()
      if (Array.isArray(mesh.material)) {
        mesh.material.forEach(mat => mat.dispose())
      } else {
        mesh.material.dispose()
      }
    }
  }

  /**
   * Benchmark memory usage patterns
   */
  async benchmarkMemoryUsage(): Promise<void> {
    console.log('💾 Benchmarking memory usage...')
    
    const initialMemory = voxelOptimizer.monitorMemoryUsage()
    
    // Create multiple grids to test memory scaling
    const grids: VoxelGrid[] = []
    const memorySnapshots: any[] = []

    for (let i = 1; i <= 5; i++) {
      const grid = createDoorVoxelGrid(200 * i, 200 * i, 18, 1.0)
      grids.push(grid)
      
      const memory = voxelOptimizer.monitorMemoryUsage()
      memorySnapshots.push({
        gridCount: i,
        heapUsed: memory.heapUsed,
        heapTotal: memory.heapTotal
      })
    }

    const result: BenchmarkResult = {
      name: 'Memory Usage Scaling',
      duration: 0,
      result: memorySnapshots,
      metadata: {
        initialMemory,
        finalMemory: voxelOptimizer.monitorMemoryUsage(),
        gridCount: grids.length
      }
    }

    this.results.set('memory_usage', result)
  }

  /**
   * Benchmark scalability with different grid sizes
   */
  async benchmarkScalability(): Promise<void> {
    console.log('📈 Benchmarking scalability...')
    
    const sizes = [
      { width: 200, height: 200, thickness: 18 },
      { width: 400, height: 400, thickness: 18 },
      { width: 600, height: 600, thickness: 18 },
      { width: 800, height: 800, thickness: 18 }
    ]

    for (const size of sizes) {
      const totalVoxels = Math.ceil(size.width / 1.0) * Math.ceil(size.height / 1.0) * Math.ceil(size.thickness / 1.0)
      
      const result = await this.measurePerformance(
        `Scalability (${size.width}x${size.height})`,
        () => {
          const grid = createDoorVoxelGrid(size.width, size.height, size.thickness, 1.0)
          const engine = new VoxelCarvingEngine(grid)
          const tool = voxelToolLibrary.getTool('endmill', 6)
          
          // Perform a standard operation
          const toolPath = {
            points: [
              { x: size.width * 0.25, y: size.height * 0.25, z: 0 },
              { x: size.width * 0.75, y: size.height * 0.75, z: 0 }
            ],
            depth: 5,
            toolType: 'endmill' as const,
            toolDiameter: 6
          }
          
          engine.simulateToolSweep(tool, toolPath)
          return grid
        }
      )

      result.metadata = {
        dimensions: size,
        totalVoxels,
        voxelsPerSecond: totalVoxels / (result.duration / 1000)
      }

      this.results.set(`scalability_${size.width}x${size.height}`, result)
    }
  }

  /**
   * Measure performance of an operation
   */
  private async measurePerformance<T>(
    name: string,
    operation: () => T
  ): Promise<BenchmarkResult> {
    const startTime = performance.now()
    const startMemory = voxelOptimizer.monitorMemoryUsage()
    
    const result = operation()
    
    const endTime = performance.now()
    const endMemory = voxelOptimizer.monitorMemoryUsage()
    const duration = endTime - startTime

    console.log(`  ✓ ${name}: ${duration.toFixed(2)}ms`)

    return {
      name,
      duration,
      result,
      metadata: {
        startMemory: startMemory.heapUsed,
        endMemory: endMemory.heapUsed,
        memoryDelta: endMemory.heapUsed - startMemory.heapUsed
      }
    }
  }

  /**
   * Generate circle points for testing
   */
  private generateCirclePoints(center: { x: number; y: number; z: number }, radius: number, segments: number) {
    const points = []
    for (let i = 0; i <= segments; i++) {
      const angle = (i / segments) * 2 * Math.PI
      points.push({
        x: center.x + radius * Math.cos(angle),
        y: center.y + radius * Math.sin(angle),
        z: center.z
      })
    }
    return points
  }

  /**
   * Generate complex path for testing
   */
  private generateComplexPath(pointCount: number) {
    const points = []
    for (let i = 0; i < pointCount; i++) {
      const t = i / (pointCount - 1)
      points.push({
        x: 100 + 400 * t + 50 * Math.sin(t * 10),
        y: 100 + 300 * Math.sin(t * 3) + 30 * Math.cos(t * 7),
        z: 0
      })
    }
    return points
  }

  /**
   * Generate performance report
   */
  private generateReport(): BenchmarkReport {
    const report: BenchmarkReport = {
      timestamp: new Date().toISOString(),
      totalTests: this.results.size,
      results: Array.from(this.results.values()),
      summary: {
        fastestOperation: '',
        slowestOperation: '',
        averageDuration: 0,
        totalDuration: 0,
        memoryEfficiency: 'Good'
      }
    }

    // Calculate summary statistics
    let totalDuration = 0
    let fastest = { name: '', duration: Infinity }
    let slowest = { name: '', duration: 0 }

    for (const result of report.results) {
      totalDuration += result.duration
      
      if (result.duration < fastest.duration) {
        fastest = { name: result.name, duration: result.duration }
      }
      
      if (result.duration > slowest.duration) {
        slowest = { name: result.name, duration: result.duration }
      }
    }

    report.summary.totalDuration = totalDuration
    report.summary.averageDuration = totalDuration / report.results.length
    report.summary.fastestOperation = `${fastest.name} (${fastest.duration.toFixed(2)}ms)`
    report.summary.slowestOperation = `${slowest.name} (${slowest.duration.toFixed(2)}ms)`

    console.log('📊 Benchmark Report Generated')
    console.log(`Total Duration: ${totalDuration.toFixed(2)}ms`)
    console.log(`Average Duration: ${report.summary.averageDuration.toFixed(2)}ms`)
    console.log(`Fastest: ${report.summary.fastestOperation}`)
    console.log(`Slowest: ${report.summary.slowestOperation}`)

    return report
  }
}

// Types
interface BenchmarkResult {
  name: string
  duration: number
  result: any
  metadata?: any
}

interface BenchmarkReport {
  timestamp: string
  totalTests: number
  results: BenchmarkResult[]
  summary: {
    fastestOperation: string
    slowestOperation: string
    averageDuration: number
    totalDuration: number
    memoryEfficiency: string
  }
}

// Export for use in tests
export const performanceBenchmark = new VoxelPerformanceBenchmark()
