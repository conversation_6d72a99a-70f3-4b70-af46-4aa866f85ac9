import type { VoxelGrid } from './voxelGrid'
import type { CuttingTool, VoxelCoordinate } from '@/types'

/**
 * VoxelPerformanceOptimizer provides caching and optimization for voxel operations
 */
export class VoxelPerformanceOptimizer {
  private toolCache = new Map<string, CuttingTool>()
  private meshCache = new Map<string, any>()
  private operationCache = new Map<string, VoxelCoordinate[]>()
  private maxCacheSize = 100

  /**
   * Cache a tool with its voxel representation
   */
  cacheTool(key: string, tool: CuttingTool): void {
    if (this.toolCache.size >= this.maxCacheSize) {
      // Remove oldest entry
      const firstKey = this.toolCache.keys().next().value
      this.toolCache.delete(firstKey)
    }
    this.toolCache.set(key, tool)
  }

  /**
   * Get cached tool
   */
  getCachedTool(key: string): CuttingTool | undefined {
    return this.toolCache.get(key)
  }

  /**
   * Cache mesh data
   */
  cacheMesh(key: string, meshData: any): void {
    if (this.meshCache.size >= this.maxCacheSize) {
      const firstKey = this.meshCache.keys().next().value
      this.meshCache.delete(firstKey)
    }
    this.meshCache.set(key, meshData)
  }

  /**
   * Get cached mesh
   */
  getCachedMesh(key: string): any {
    return this.meshCache.get(key)
  }

  /**
   * Cache operation results
   */
  cacheOperation(key: string, voxels: VoxelCoordinate[]): void {
    if (this.operationCache.size >= this.maxCacheSize) {
      const firstKey = this.operationCache.keys().next().value
      this.operationCache.delete(firstKey)
    }
    this.operationCache.set(key, voxels)
  }

  /**
   * Get cached operation
   */
  getCachedOperation(key: string): VoxelCoordinate[] | undefined {
    return this.operationCache.get(key)
  }

  /**
   * Clear all caches
   */
  clearCaches(): void {
    this.toolCache.clear()
    this.meshCache.clear()
    this.operationCache.clear()
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): {
    toolCacheSize: number
    meshCacheSize: number
    operationCacheSize: number
    totalMemoryEstimateMB: number
  } {
    // Rough memory estimates
    const toolMemory = this.toolCache.size * 1000 // ~1KB per tool
    const meshMemory = this.meshCache.size * 50000 // ~50KB per mesh
    const operationMemory = this.operationCache.size * 5000 // ~5KB per operation
    
    return {
      toolCacheSize: this.toolCache.size,
      meshCacheSize: this.meshCache.size,
      operationCacheSize: this.operationCache.size,
      totalMemoryEstimateMB: (toolMemory + meshMemory + operationMemory) / (1024 * 1024)
    }
  }

  /**
   * Optimize voxel grid operations using spatial partitioning
   */
  optimizeGridOperations(grid: VoxelGrid): {
    partitions: Map<string, VoxelCoordinate[]>
    partitionSize: number
  } {
    const dimensions = grid.getDimensions()
    const partitionSize = 32 // 32x32x32 voxel partitions
    const partitions = new Map<string, VoxelCoordinate[]>()

    // Create spatial partitions
    for (let px = 0; px < Math.ceil(dimensions[0] / partitionSize); px++) {
      for (let py = 0; py < Math.ceil(dimensions[1] / partitionSize); py++) {
        for (let pz = 0; pz < Math.ceil(dimensions[2] / partitionSize); pz++) {
          const partitionKey = `${px},${py},${pz}`
          const voxels: VoxelCoordinate[] = []

          // Collect voxels in this partition
          for (let x = px * partitionSize; x < Math.min((px + 1) * partitionSize, dimensions[0]); x++) {
            for (let y = py * partitionSize; y < Math.min((py + 1) * partitionSize, dimensions[1]); y++) {
              for (let z = pz * partitionSize; z < Math.min((pz + 1) * partitionSize, dimensions[2]); z++) {
                if (grid.getVoxel({ x, y, z }) === 1) {
                  voxels.push({ x, y, z })
                }
              }
            }
          }

          if (voxels.length > 0) {
            partitions.set(partitionKey, voxels)
          }
        }
      }
    }

    return { partitions, partitionSize }
  }

  /**
   * Batch process multiple operations for better performance
   */
  batchProcessOperations<T>(
    operations: T[],
    processor: (operation: T) => void,
    batchSize: number = 10,
    progressCallback?: (progress: number) => void
  ): Promise<void> {
    return new Promise((resolve) => {
      let currentIndex = 0

      const processBatch = () => {
        const endIndex = Math.min(currentIndex + batchSize, operations.length)
        
        for (let i = currentIndex; i < endIndex; i++) {
          processor(operations[i])
        }

        currentIndex = endIndex

        if (progressCallback) {
          progressCallback((currentIndex / operations.length) * 100)
        }

        if (currentIndex < operations.length) {
          // Schedule next batch
          setTimeout(processBatch, 0)
        } else {
          resolve()
        }
      }

      processBatch()
    })
  }

  /**
   * Optimize tool voxel generation with level-of-detail
   */
  generateOptimizedToolVoxels(
    toolType: 'endmill' | 'ballnose' | 'chamfer',
    diameter: number,
    height: number,
    resolution: number,
    lodLevel: number = 1
  ): VoxelCoordinate[] {
    const cacheKey = `${toolType}-${diameter}-${height}-${resolution}-${lodLevel}`
    
    // Check cache first
    const cached = this.getCachedOperation(cacheKey)
    if (cached) {
      return cached
    }

    // Generate with LOD optimization
    const effectiveResolution = resolution * lodLevel
    const radiusVoxels = Math.ceil((diameter / 2) / effectiveResolution)
    const heightVoxels = Math.ceil(height / effectiveResolution)
    const offsets: VoxelCoordinate[] = []

    switch (toolType) {
      case 'endmill':
        for (let z = 0; z < heightVoxels; z += lodLevel) {
          for (let x = -radiusVoxels; x <= radiusVoxels; x += lodLevel) {
            for (let y = -radiusVoxels; y <= radiusVoxels; y += lodLevel) {
              const distance = Math.sqrt(x * x + y * y)
              if (distance <= radiusVoxels) {
                offsets.push({ x, y, z })
              }
            }
          }
        }
        break

      case 'ballnose':
        // Simplified ballnose for performance
        for (let z = 0; z < heightVoxels; z += lodLevel) {
          const radius = z < radiusVoxels ? 
            Math.sqrt(radiusVoxels * radiusVoxels - (radiusVoxels - z) * (radiusVoxels - z)) :
            radiusVoxels

          for (let x = -Math.ceil(radius); x <= Math.ceil(radius); x += lodLevel) {
            for (let y = -Math.ceil(radius); y <= Math.ceil(radius); y += lodLevel) {
              const distance = Math.sqrt(x * x + y * y)
              if (distance <= radius) {
                offsets.push({ x, y, z })
              }
            }
          }
        }
        break

      case 'chamfer':
        for (let z = 0; z < heightVoxels; z += lodLevel) {
          const radiusAtZ = (z / heightVoxels) * radiusVoxels
          for (let x = -Math.ceil(radiusAtZ); x <= Math.ceil(radiusAtZ); x += lodLevel) {
            for (let y = -Math.ceil(radiusAtZ); y <= Math.ceil(radiusAtZ); y += lodLevel) {
              const distance = Math.sqrt(x * x + y * y)
              if (distance <= radiusAtZ) {
                offsets.push({ x, y, z })
              }
            }
          }
        }
        break
    }

    // Cache the result
    this.cacheOperation(cacheKey, offsets)
    return offsets
  }

  /**
   * Memory usage monitoring
   */
  monitorMemoryUsage(): {
    heapUsed: number
    heapTotal: number
    external: number
    arrayBuffers: number
  } {
    if (typeof performance !== 'undefined' && (performance as any).memory) {
      const memory = (performance as any).memory
      return {
        heapUsed: memory.usedJSHeapSize / (1024 * 1024),
        heapTotal: memory.totalJSHeapSize / (1024 * 1024),
        external: memory.usedJSHeapSize / (1024 * 1024), // Approximation
        arrayBuffers: 0 // Not available in browser
      }
    }
    
    return {
      heapUsed: 0,
      heapTotal: 0,
      external: 0,
      arrayBuffers: 0
    }
  }

  /**
   * Performance profiler for operations
   */
  profileOperation<T>(name: string, operation: () => T): { result: T; duration: number } {
    const startTime = performance.now()
    const result = operation()
    const endTime = performance.now()
    const duration = endTime - startTime

    console.log(`Performance: ${name} took ${duration.toFixed(2)}ms`)
    return { result, duration }
  }

  /**
   * Adaptive quality settings based on performance
   */
  getAdaptiveQualitySettings(targetFPS: number = 30): {
    voxelResolution: number
    meshAlgorithm: 'greedy' | 'marching'
    lodLevel: number
  } {
    const memoryInfo = this.monitorMemoryUsage()
    const isLowMemory = memoryInfo.heapUsed > 100 // > 100MB

    if (isLowMemory) {
      return {
        voxelResolution: 1.0,
        meshAlgorithm: 'greedy',
        lodLevel: 2
      }
    } else {
      return {
        voxelResolution: 0.5,
        meshAlgorithm: 'marching',
        lodLevel: 1
      }
    }
  }
}

// Export singleton instance
export const voxelOptimizer = new VoxelPerformanceOptimizer()
