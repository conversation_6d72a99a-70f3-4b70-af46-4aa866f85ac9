import type { VoxelCarveOperation, CuttingTool, ToolPath, WorldCoordinate } from '@/types'
import { VoxelGrid } from './voxelGrid'
import { VoxelPathProcessor } from './voxelPathProcessor'

/**
 * VoxelCarvingEngine performs the actual material removal simulation
 */
export class VoxelCarvingEngine {
  private grid: VoxelGrid
  private pathProcessor: VoxelPathProcessor
  private operations: VoxelCarveOperation[] = []

  constructor(grid: VoxelGrid) {
    this.grid = grid
    this.pathProcessor = new VoxelPathProcessor(grid.getConfig().resolution)
  }

  /**
   * Add a carving operation to the queue
   */
  addOperation(operation: VoxelCarveOperation): void {
    this.operations.push(operation)
  }

  /**
   * Execute all queued operations
   */
  executeOperations(progressCallback?: (progress: number, operation: string) => void): void {
    const totalOps = this.operations.length
    
    for (let i = 0; i < totalOps; i++) {
      const operation = this.operations[i]
      
      if (progressCallback) {
        progressCallback((i / totalOps) * 100, `Processing operation ${i + 1}/${totalOps}`)
      }

      this.executeOperation(operation)
    }

    if (progressCallback) {
      progressCallback(100, 'Carving complete')
    }
  }

  /**
   * Execute a single carving operation
   */
  private executeOperation(operation: VoxelCarveOperation): void {
    const { tool, path, startZ, face } = operation
    
    if (!tool.voxelOffsets) {
      console.warn('Tool has no voxel offsets, skipping operation')
      return
    }

    // Interpolate the path for smooth carving
    const interpolatedPoints = this.pathProcessor.interpolatePath(path.points, 0.25)
    
    // Apply tool at each point along the path
    for (const point of interpolatedPoints) {
      // Calculate the actual Z position based on face and depth
      const actualZ = this.calculateToolZ(point, startZ, path.depth, face)
      
      const toolPosition: WorldCoordinate = {
        x: point.x,
        y: point.y,
        z: actualZ
      }

      this.grid.applyTool(tool.voxelOffsets, toolPosition)
    }
  }

  /**
   * Calculate the Z position for the tool based on face and depth
   */
  private calculateToolZ(
    pathPoint: WorldCoordinate,
    startZ: number,
    depth: number,
    face: 'top' | 'bottom'
  ): number {
    const gridConfig = this.grid.getConfig()
    
    if (face === 'top') {
      // For top face, start from the top surface and go down
      return gridConfig.thickness - startZ - depth
    } else {
      // For bottom face, start from the bottom surface and go up
      return startZ + depth
    }
  }

  /**
   * Simulate a single tool sweep along a path
   */
  simulateToolSweep(
    tool: CuttingTool,
    path: ToolPath,
    startZ: number = 0,
    face: 'top' | 'bottom' = 'top'
  ): void {
    const operation: VoxelCarveOperation = {
      tool,
      path,
      startZ,
      face
    }

    this.executeOperation(operation)
  }

  /**
   * Perform pocket operation (area clearing)
   */
  simulatePocketOperation(
    tool: CuttingTool,
    center: WorldCoordinate,
    radius: number,
    depth: number,
    stepDown: number = 1,
    face: 'top' | 'bottom' = 'top'
  ): void {
    const toolRadius = tool.diameter / 2
    const passes = Math.ceil(depth / stepDown)

    for (let pass = 0; pass < passes; pass++) {
      const currentDepth = Math.min((pass + 1) * stepDown, depth)
      
      // Generate spiral toolpath for this depth
      const pocketPoints = this.pathProcessor.generatePocketToolpath(
        center,
        radius,
        toolRadius,
        toolRadius * 0.8 // 80% stepover
      )

      if (pocketPoints.length > 0) {
        const toolPath: ToolPath = {
          points: pocketPoints,
          depth: currentDepth,
          toolType: tool.type,
          toolDiameter: tool.diameter
        }

        this.simulateToolSweep(tool, toolPath, pass * stepDown, face)
      }
    }
  }

  /**
   * Perform groove operation along a path
   */
  simulateGrooveOperation(
    tool: CuttingTool,
    points: WorldCoordinate[],
    depth: number,
    face: 'top' | 'bottom' = 'top'
  ): void {
    const toolPath: ToolPath = {
      points,
      depth,
      toolType: tool.type,
      toolDiameter: tool.diameter
    }

    this.simulateToolSweep(tool, toolPath, 0, face)
  }

  /**
   * Perform drilling operation
   */
  simulateDrillOperation(
    tool: CuttingTool,
    position: WorldCoordinate,
    depth: number,
    face: 'top' | 'bottom' = 'top'
  ): void {
    const drillPath: ToolPath = {
      points: [position, position], // Single point, but needs array
      depth,
      toolType: tool.type,
      toolDiameter: tool.diameter
    }

    this.simulateToolSweep(tool, drillPath, 0, face)
  }

  /**
   * Clear all operations
   */
  clearOperations(): void {
    this.operations = []
  }

  /**
   * Get current operations queue
   */
  getOperations(): VoxelCarveOperation[] {
    return [...this.operations]
  }

  /**
   * Reset the grid to original state
   */
  resetGrid(): void {
    this.grid.reset()
  }

  /**
   * Get the current grid state
   */
  getGrid(): VoxelGrid {
    return this.grid
  }

  /**
   * Calculate material removal volume
   */
  calculateMaterialRemoval(): { removedVoxels: number; removedVolumeMM3: number } {
    const dimensions = this.grid.getDimensions()
    const config = this.grid.getConfig()
    const voxelVolume = config.resolution ** 3
    
    let removedVoxels = 0
    
    for (let x = 0; x < dimensions[0]; x++) {
      for (let y = 0; y < dimensions[1]; y++) {
        for (let z = 0; z < dimensions[2]; z++) {
          if (this.grid.getVoxel({ x, y, z }) === 0) {
            removedVoxels++
          }
        }
      }
    }

    return {
      removedVoxels,
      removedVolumeMM3: removedVoxels * voxelVolume
    }
  }

  /**
   * Get carving statistics
   */
  getStatistics(): {
    totalOperations: number
    materialRemoval: { removedVoxels: number; removedVolumeMM3: number }
    gridDimensions: [number, number, number]
    resolution: number
  } {
    return {
      totalOperations: this.operations.length,
      materialRemoval: this.calculateMaterialRemoval(),
      gridDimensions: this.grid.getDimensions(),
      resolution: this.grid.getConfig().resolution
    }
  }

  /**
   * Export carving operations as JSON for debugging
   */
  exportOperations(): string {
    return JSON.stringify(this.operations, null, 2)
  }

  /**
   * Import carving operations from JSON
   */
  importOperations(json: string): void {
    try {
      const operations = JSON.parse(json) as VoxelCarveOperation[]
      this.operations = operations
    } catch (error) {
      console.error('Failed to import operations:', error)
      throw new Error('Invalid operations JSON format')
    }
  }

  /**
   * Create a preview of the operation without actually executing it
   */
  previewOperation(operation: VoxelCarveOperation): WorldCoordinate[] {
    const interpolatedPoints = this.pathProcessor.interpolatePath(operation.path.points, 0.5)
    
    return interpolatedPoints.map(point => ({
      x: point.x,
      y: point.y,
      z: this.calculateToolZ(point, operation.startZ, operation.path.depth, operation.face)
    }))
  }
}
