// Type declarations for modules without TypeScript support

declare module 'greedy-mesher' {
  interface MeshData {
    vertices: number[]
    faces: number[]
  }
  
  function greedyMesher(voxels: number[][][]): MeshData
  export = greedyMesher
}

declare module 'isosurface' {
  interface MarchingCubesResult {
    positions: number[]
    cells?: number[][]
  }
  
  interface IsosurfaceModule {
    marchingCubes(
      dims: [number, number, number],
      potential: (x: number, y: number, z: number) => number,
      bounds?: [[number, number], [number, number], [number, number]]
    ): MarchingCubesResult
    
    marchingCubes(
      dims: [number, number, number],
      potential: (x: number, y: number, z: number) => number,
      isovalue?: number
    ): MarchingCubesResult
  }
  
  const isosurface: IsosurfaceModule
  export = isosurface
}

declare module 'leva' {
  export interface LevaInputs {
    [key: string]: any
  }
  
  export function useControls(inputs: LevaInputs): any
  export function folder(name: string, inputs: LevaInputs): any
  export function button(fn: () => void): any
}
