<template>
  <div class="voxel-viewer-container">
    <!-- Controls Panel -->
    <div class="controls-panel">
      <div class="control-group">
        <label>Algorithm:</label>
        <select v-model="meshAlgorithm" @change="regenerateMesh">
          <option value="greedy"><PERSON><PERSON><PERSON>sher (Fast)</option>
          <option value="marching">Marching Cubes (Smooth)</option>
        </select>
      </div>
      
      <div class="control-group">
        <label>Material:</label>
        <select v-model="materialType" @change="updateMaterial">
          <option value="standard">Standard</option>
          <option value="wireframe">Wireframe</option>
          <option value="depth">Depth Colored</option>
        </select>
      </div>

      <div class="control-group">
        <button @click="resetView" class="control-button">
          Reset View
        </button>
        <button @click="toggleRotation" class="control-button">
          {{ autoRotate ? 'Stop' : 'Start' }} Rotation
        </button>
      </div>

      <div class="stats-panel">
        <div class="stat-item">
          <span>Vertices:</span>
          <span>{{ meshStats.vertices.toLocaleString() }}</span>
        </div>
        <div class="stat-item">
          <span>Faces:</span>
          <span>{{ meshStats.faces.toLocaleString() }}</span>
        </div>
        <div class="stat-item">
          <span>Memory:</span>
          <span>{{ meshStats.memoryMB.toFixed(2) }} MB</span>
        </div>
      </div>
    </div>

    <!-- 3D Canvas -->
    <div ref="canvasContainer" class="canvas-container">
      <canvas ref="canvas"></canvas>
    </div>

    <!-- Loading Overlay -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <p>{{ loadingMessage }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as THREE from 'three'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'
import { VoxelGrid } from '@/services/voxelGrid'
import { VoxelMeshGenerator } from '@/services/voxelMeshGenerator'

interface Props {
  voxelGrid?: VoxelGrid
  autoRegenerate?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  autoRegenerate: true
})

// Template refs
const canvas = ref<HTMLCanvasElement>()
const canvasContainer = ref<HTMLDivElement>()

// Three.js objects
let scene: THREE.Scene
let camera: THREE.PerspectiveCamera
let renderer: THREE.WebGLRenderer
let controls: OrbitControls
let meshGenerator: VoxelMeshGenerator
let currentMesh: THREE.Mesh | null = null

// Reactive state
const meshAlgorithm = ref<'greedy' | 'marching'>('greedy')
const materialType = ref<'standard' | 'wireframe' | 'depth'>('standard')
const autoRotate = ref(false)
const isLoading = ref(false)
const loadingMessage = ref('')
const meshStats = ref({
  vertices: 0,
  faces: 0,
  memoryMB: 0
})

// Initialize Three.js scene
const initThreeJS = () => {
  if (!canvas.value || !canvasContainer.value) return

  // Scene
  scene = new THREE.Scene()
  scene.background = new THREE.Color(0xf0f0f0)

  // Camera
  camera = new THREE.PerspectiveCamera(
    75,
    canvasContainer.value.clientWidth / canvasContainer.value.clientHeight,
    0.1,
    1000
  )
  camera.position.set(50, 50, 50)

  // Renderer
  renderer = new THREE.WebGLRenderer({
    canvas: canvas.value,
    antialias: true
  })
  renderer.setSize(canvasContainer.value.clientWidth, canvasContainer.value.clientHeight)
  renderer.setPixelRatio(window.devicePixelRatio)
  renderer.shadowMap.enabled = true
  renderer.shadowMap.type = THREE.PCFSoftShadowMap

  // Controls
  controls = new OrbitControls(camera, renderer.domElement)
  controls.enableDamping = true
  controls.dampingFactor = 0.05
  controls.autoRotate = autoRotate.value

  // Lighting
  setupLighting()

  // Grid helper
  const gridHelper = new THREE.GridHelper(100, 20, 0x888888, 0xcccccc)
  scene.add(gridHelper)

  // Axes helper
  const axesHelper = new THREE.AxesHelper(20)
  scene.add(axesHelper)

  // Mesh generator
  meshGenerator = new VoxelMeshGenerator(props.voxelGrid?.getConfig().resolution || 0.5)

  // Start render loop
  animate()
}

// Setup lighting
const setupLighting = () => {
  // Ambient light
  const ambientLight = new THREE.AmbientLight(0x404040, 0.4)
  scene.add(ambientLight)

  // Directional light
  const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8)
  directionalLight.position.set(50, 50, 25)
  directionalLight.castShadow = true
  directionalLight.shadow.mapSize.width = 2048
  directionalLight.shadow.mapSize.height = 2048
  scene.add(directionalLight)

  // Point light for fill
  const pointLight = new THREE.PointLight(0xffffff, 0.3, 100)
  pointLight.position.set(-25, 25, 25)
  scene.add(pointLight)
}

// Animation loop
const animate = () => {
  requestAnimationFrame(animate)
  
  controls.update()
  renderer.render(scene, camera)
}

// Generate mesh from voxel grid
const generateMesh = async () => {
  if (!props.voxelGrid) {
    console.log('No voxel grid available for mesh generation')
    return
  }

  if (isLoading.value) {
    console.log('Mesh generation already in progress')
    return
  }

  isLoading.value = true
  loadingMessage.value = 'Generating mesh...'

  try {
    await nextTick() // Allow UI to update

    // Safety check for grid size
    const dimensions = props.voxelGrid.getDimensions()
    const totalVoxels = dimensions[0] * dimensions[1] * dimensions[2]

    if (totalVoxels > 10_000_000) { // 10M voxel limit for mesh generation
      throw new Error(`Grid too large for mesh generation: ${(totalVoxels / 1_000_000).toFixed(1)}M voxels. Please use a lower resolution.`)
    }

    // Remove existing mesh
    if (currentMesh) {
      scene.remove(currentMesh)
      currentMesh.geometry.dispose()
      if (Array.isArray(currentMesh.material)) {
        currentMesh.material.forEach(mat => mat.dispose())
      } else {
        currentMesh.material.dispose()
      }
    }

    // Generate new mesh
    let mesh: THREE.Mesh

    switch (materialType.value) {
      case 'wireframe':
        mesh = meshGenerator.generateWireframeMesh(props.voxelGrid)
        break
      case 'depth':
        mesh = meshGenerator.generateDepthColoredMesh(props.voxelGrid)
        break
      default:
        mesh = meshGenerator.generateMeshWithMaterial(
          props.voxelGrid,
          { color: 0xd4a574, roughness: 0.8, metalness: 0.1 },
          meshAlgorithm.value
        )
    }

    mesh.castShadow = true
    mesh.receiveShadow = true

    // Center the mesh
    const box = new THREE.Box3().setFromObject(mesh)
    const center = box.getCenter(new THREE.Vector3())
    mesh.position.sub(center)

    scene.add(mesh)
    currentMesh = mesh

    // Update stats
    meshStats.value = meshGenerator.getMeshStats(mesh)

    // Fit camera to object
    fitCameraToObject()

  } catch (error) {
    console.error('Failed to generate mesh:', error)
  } finally {
    isLoading.value = false
  }
}

// Regenerate mesh with current settings
const regenerateMesh = () => {
  generateMesh()
}

// Update material without regenerating geometry
const updateMaterial = () => {
  generateMesh() // For now, regenerate the whole mesh
}

// Fit camera to show the entire object
const fitCameraToObject = () => {
  if (!currentMesh) return

  const box = new THREE.Box3().setFromObject(currentMesh)
  const size = box.getSize(new THREE.Vector3())
  const maxDim = Math.max(size.x, size.y, size.z)
  
  const distance = maxDim * 2
  camera.position.set(distance, distance, distance)
  camera.lookAt(0, 0, 0)
  
  controls.target.set(0, 0, 0)
  controls.update()
}

// Reset camera view
const resetView = () => {
  fitCameraToObject()
}

// Toggle auto rotation
const toggleRotation = () => {
  autoRotate.value = !autoRotate.value
  controls.autoRotate = autoRotate.value
}

// Handle window resize
const handleResize = () => {
  if (!canvasContainer.value || !camera || !renderer) return

  const width = canvasContainer.value.clientWidth
  const height = canvasContainer.value.clientHeight

  camera.aspect = width / height
  camera.updateProjectionMatrix()
  renderer.setSize(width, height)
}

// Watch for voxel grid changes
watch(() => props.voxelGrid, () => {
  if (props.autoRegenerate && props.voxelGrid) {
    generateMesh()
  }
}, { immediate: true })

// Lifecycle
onMounted(() => {
  initThreeJS()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  
  // Cleanup Three.js resources
  if (currentMesh) {
    scene.remove(currentMesh)
    currentMesh.geometry.dispose()
    if (Array.isArray(currentMesh.material)) {
      currentMesh.material.forEach(mat => mat.dispose())
    } else {
      currentMesh.material.dispose()
    }
  }
  
  if (renderer) {
    renderer.dispose()
  }
})

// Expose methods for parent components
defineExpose({
  regenerateMesh,
  resetView,
  fitCameraToObject
})
</script>

<style scoped>
.voxel-viewer-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.controls-panel {
  background: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  padding: 0.75rem;
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.control-group label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #495057;
}

.control-group select {
  padding: 0.25rem 0.5rem;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  font-size: 0.875rem;
}

.control-button {
  padding: 0.375rem 0.75rem;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.15s;
}

.control-button:hover {
  background: #0056b3;
}

.stats-panel {
  margin-left: auto;
  display: flex;
  gap: 1rem;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 0.75rem;
}

.stat-item span:first-child {
  color: #6c757d;
  font-weight: 500;
}

.stat-item span:last-child {
  color: #495057;
  font-weight: 600;
}

.canvas-container {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.canvas-container canvas {
  display: block;
  width: 100%;
  height: 100%;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.loading-content {
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
