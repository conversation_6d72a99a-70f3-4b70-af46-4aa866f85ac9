import type { ToolPath, WorldCoordinate, DrawCommand, CuttingTool } from '@/types'

/**
 * VoxelPathProcessor handles toolpath generation and interpolation for voxel carving
 */
export class VoxelPathProcessor {
  private resolution: number

  constructor(resolution: number = 0.5) {
    this.resolution = resolution
  }

  /**
   * Convert DrawCommands to ToolPaths for voxel processing
   */
  convertDrawCommandsToToolPaths(
    drawCommands: DrawCommand[],
    defaultDepth: number = 5,
    defaultTool: CuttingTool
  ): ToolPath[] {
    const toolPaths: ToolPath[] = []

    for (const command of drawCommands) {
      const depth = command.thickness ? Math.abs(command.thickness) : defaultDepth
      const points = this.extractPointsFromCommand(command)
      
      if (points.length > 0) {
        toolPaths.push({
          points,
          depth,
          toolType: defaultTool.type,
          toolDiameter: defaultTool.diameter
        })
      }
    }

    return toolPaths
  }

  /**
   * Extract world coordinates from a DrawCommand
   */
  private extractPointsFromCommand(command: DrawCommand): WorldCoordinate[] {
    const points: WorldCoordinate[] = []

    switch (command.command_type) {
      case 'line':
        points.push(
          { x: command.x1, y: command.y1, z: command.z1 || 0 },
          { x: command.x2, y: command.y2, z: command.z2 || 0 }
        )
        break

      case 'polyline':
      case 'polygon':
        if (command.points) {
          for (const point of command.points) {
            points.push({
              x: point[0],
              y: point[1],
              z: point[2] || 0
            })
          }
        }
        break

      case 'circle':
        // Generate circle points
        const circlePoints = this.generateCirclePoints(
          { x: command.x1, y: command.y1, z: command.z1 || 0 },
          command.radius,
          32 // segments
        )
        points.push(...circlePoints)
        break

      case 'arc':
        // Generate arc points
        const arcPoints = this.generateArcPoints(
          { x: command.x1, y: command.y1, z: command.z1 || 0 },
          command.radius,
          command.start_angle || 0,
          command.end_angle || 90,
          command.clockwise || false,
          16 // segments
        )
        points.push(...arcPoints)
        break

      case 'rectangle':
        // Generate rectangle points
        const rectPoints = this.generateRectanglePoints(
          { x: command.x1, y: command.y1, z: command.z1 || 0 },
          { x: command.x2, y: command.y2, z: command.z2 || 0 }
        )
        points.push(...rectPoints)
        break
    }

    return points
  }

  /**
   * Generate points along a circle
   */
  private generateCirclePoints(center: WorldCoordinate, radius: number, segments: number): WorldCoordinate[] {
    const points: WorldCoordinate[] = []
    
    for (let i = 0; i <= segments; i++) {
      const angle = (i / segments) * 2 * Math.PI
      points.push({
        x: center.x + radius * Math.cos(angle),
        y: center.y + radius * Math.sin(angle),
        z: center.z
      })
    }

    return points
  }

  /**
   * Generate points along an arc
   */
  private generateArcPoints(
    center: WorldCoordinate,
    radius: number,
    startAngle: number,
    endAngle: number,
    clockwise: boolean,
    segments: number
  ): WorldCoordinate[] {
    const points: WorldCoordinate[] = []
    
    // Convert angles to radians
    let startRad = (startAngle * Math.PI) / 180
    let endRad = (endAngle * Math.PI) / 180
    
    // Handle clockwise direction
    if (clockwise) {
      if (endRad > startRad) {
        endRad -= 2 * Math.PI
      }
    } else {
      if (endRad < startRad) {
        endRad += 2 * Math.PI
      }
    }

    const angleRange = Math.abs(endRad - startRad)
    const segmentCount = Math.max(4, Math.ceil(segments * (angleRange / (2 * Math.PI))))

    for (let i = 0; i <= segmentCount; i++) {
      const t = i / segmentCount
      const angle = startRad + (endRad - startRad) * t
      
      points.push({
        x: center.x + radius * Math.cos(angle),
        y: center.y + radius * Math.sin(angle),
        z: center.z
      })
    }

    return points
  }

  /**
   * Generate points for a rectangle outline
   */
  private generateRectanglePoints(corner1: WorldCoordinate, corner2: WorldCoordinate): WorldCoordinate[] {
    const minX = Math.min(corner1.x, corner2.x)
    const maxX = Math.max(corner1.x, corner2.x)
    const minY = Math.min(corner1.y, corner2.y)
    const maxY = Math.max(corner1.y, corner2.y)
    const z = corner1.z

    return [
      { x: minX, y: minY, z },
      { x: maxX, y: minY, z },
      { x: maxX, y: maxY, z },
      { x: minX, y: maxY, z },
      { x: minX, y: minY, z } // Close the rectangle
    ]
  }

  /**
   * Interpolate points along a path with specified step size
   */
  interpolatePath(points: WorldCoordinate[], stepSize: number = 0.5): WorldCoordinate[] {
    if (points.length < 2) return points

    const interpolated: WorldCoordinate[] = []
    interpolated.push(points[0])

    for (let i = 1; i < points.length; i++) {
      const start = points[i - 1]
      const end = points[i]
      
      const distance = this.calculateDistance(start, end)
      const steps = Math.ceil(distance / stepSize)

      for (let step = 1; step <= steps; step++) {
        const t = step / steps
        interpolated.push({
          x: start.x + (end.x - start.x) * t,
          y: start.y + (end.y - start.y) * t,
          z: start.z + (end.z - start.z) * t
        })
      }
    }

    return interpolated
  }

  /**
   * Calculate 3D distance between two points
   */
  private calculateDistance(p1: WorldCoordinate, p2: WorldCoordinate): number {
    const dx = p2.x - p1.x
    const dy = p2.y - p1.y
    const dz = p2.z - p1.z
    return Math.sqrt(dx * dx + dy * dy + dz * dz)
  }

  /**
   * Generate toolpath for pocket operation (spiral inward)
   */
  generatePocketToolpath(
    center: WorldCoordinate,
    radius: number,
    toolRadius: number,
    stepOver: number = 0.5
  ): WorldCoordinate[] {
    const points: WorldCoordinate[] = []
    const maxRadius = radius - toolRadius
    
    if (maxRadius <= 0) return points

    let currentRadius = maxRadius
    let angle = 0
    const angleStep = 0.1 // radians

    while (currentRadius > toolRadius) {
      points.push({
        x: center.x + currentRadius * Math.cos(angle),
        y: center.y + currentRadius * Math.sin(angle),
        z: center.z
      })

      angle += angleStep
      
      // Spiral inward
      const spiralRate = stepOver / (2 * Math.PI)
      currentRadius -= spiralRate * angleStep
    }

    return points
  }

  /**
   * Generate raster toolpath for area clearing
   */
  generateRasterToolpath(
    bounds: { minX: number; maxX: number; minY: number; maxY: number },
    stepOver: number,
    z: number = 0
  ): WorldCoordinate[] {
    const points: WorldCoordinate[] = []
    let y = bounds.minY
    let direction = 1 // 1 for left-to-right, -1 for right-to-left

    while (y <= bounds.maxY) {
      if (direction === 1) {
        // Left to right
        points.push({ x: bounds.minX, y, z })
        points.push({ x: bounds.maxX, y, z })
      } else {
        // Right to left
        points.push({ x: bounds.maxX, y, z })
        points.push({ x: bounds.minX, y, z })
      }

      y += stepOver
      direction *= -1
    }

    return points
  }

  /**
   * Optimize toolpath by removing redundant points
   */
  optimizeToolpath(points: WorldCoordinate[], tolerance: number = 0.1): WorldCoordinate[] {
    if (points.length < 3) return points

    const optimized: WorldCoordinate[] = [points[0]]

    for (let i = 1; i < points.length - 1; i++) {
      const prev = optimized[optimized.length - 1]
      const current = points[i]
      const next = points[i + 1]

      // Check if current point is on the line between prev and next
      if (!this.isPointOnLine(prev, current, next, tolerance)) {
        optimized.push(current)
      }
    }

    optimized.push(points[points.length - 1])
    return optimized
  }

  /**
   * Check if a point lies on the line between two other points within tolerance
   */
  private isPointOnLine(
    p1: WorldCoordinate,
    p2: WorldCoordinate,
    p3: WorldCoordinate,
    tolerance: number
  ): boolean {
    // Calculate distance from p2 to line p1-p3
    const A = p3.y - p1.y
    const B = p1.x - p3.x
    const C = p3.x * p1.y - p1.x * p3.y
    
    const distance = Math.abs(A * p2.x + B * p2.y + C) / Math.sqrt(A * A + B * B)
    return distance <= tolerance
  }
}
