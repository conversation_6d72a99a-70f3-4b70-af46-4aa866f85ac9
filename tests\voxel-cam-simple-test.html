<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voxel CAM Simple Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧊 Voxel CAM Simple Test</h1>
        <p>Basic functionality test for the voxel-based CAM system</p>

        <div class="test-section">
            <h3>1. Grid Creation Test</h3>
            <p>Test creating a small voxel grid</p>
            <button class="test-button" onclick="testGridCreation()">Test Grid Creation</button>
            <div id="gridResult"></div>
        </div>

        <div class="test-section">
            <h3>2. Tool Generation Test</h3>
            <p>Test generating tool voxel representations</p>
            <button class="test-button" onclick="testToolGeneration()">Test Tool Generation</button>
            <div id="toolResult"></div>
        </div>

        <div class="test-section">
            <h3>3. Mesh Generation Test</h3>
            <p>Test converting voxel grid to mesh</p>
            <button class="test-button" onclick="testMeshGeneration()" id="meshButton" disabled>Test Mesh Generation</button>
            <div id="meshResult"></div>
        </div>

        <div class="test-section">
            <h3>4. Performance Test</h3>
            <p>Test with different grid sizes</p>
            <button class="test-button" onclick="testPerformance()">Test Performance</button>
            <div id="perfResult"></div>
        </div>

        <div class="test-section">
            <h3>Test Log</h3>
            <div id="testLog" class="log"></div>
        </div>
    </div>

    <script type="module">
        // Simple test framework
        let testGrid = null;
        let testTool = null;

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('testLog');
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="result ${type}">${message}</div>`;
        }

        // Test 1: Grid Creation
        window.testGridCreation = async function() {
            log('Testing grid creation...');
            
            try {
                // Simulate grid creation (since we can't import modules in this simple test)
                const gridConfig = {
                    width: 100,
                    height: 100,
                    thickness: 18,
                    resolution: 2.0 // Use large resolution for fast test
                };

                const dimensions = [
                    Math.ceil(gridConfig.width / gridConfig.resolution),
                    Math.ceil(gridConfig.height / gridConfig.resolution),
                    Math.ceil(gridConfig.thickness / gridConfig.resolution)
                ];

                const totalVoxels = dimensions[0] * dimensions[1] * dimensions[2];
                const memoryMB = totalVoxels / (1024 * 1024);

                log(`Grid dimensions: ${dimensions.join('x')} voxels`);
                log(`Total voxels: ${totalVoxels.toLocaleString()}`);
                log(`Memory usage: ${memoryMB.toFixed(2)} MB`);

                if (totalVoxels > 1000000) {
                    throw new Error('Grid too large for test');
                }

                testGrid = { dimensions, totalVoxels, memoryMB };
                
                showResult('gridResult', 
                    `✓ Grid created successfully<br>
                     Dimensions: ${dimensions.join('×')} voxels<br>
                     Memory: ${memoryMB.toFixed(2)} MB`, 
                    'success'
                );

                // Enable mesh test
                document.getElementById('meshButton').disabled = false;
                
            } catch (error) {
                log(`Grid creation failed: ${error.message}`, 'error');
                showResult('gridResult', `✗ Grid creation failed: ${error.message}`, 'error');
            }
        };

        // Test 2: Tool Generation
        window.testToolGeneration = async function() {
            log('Testing tool generation...');
            
            try {
                // Simulate tool generation
                const toolConfigs = [
                    { type: 'endmill', diameter: 6, height: 10 },
                    { type: 'ballnose', diameter: 8, height: 12 },
                    { type: 'chamfer', diameter: 10, height: 10 }
                ];

                const tools = [];
                const resolution = 1.0;

                for (const config of toolConfigs) {
                    const radiusVoxels = Math.ceil((config.diameter / 2) / resolution);
                    const heightVoxels = Math.ceil(config.height / resolution);
                    const voxelCount = radiusVoxels * radiusVoxels * heightVoxels * Math.PI / 4; // Rough estimate

                    tools.push({
                        ...config,
                        voxelCount: Math.floor(voxelCount)
                    });

                    log(`${config.type} tool: ${config.diameter}mm, ~${Math.floor(voxelCount)} voxels`);
                }

                testTool = tools[0]; // Use first tool for mesh test

                showResult('toolResult', 
                    `✓ Generated ${tools.length} tools<br>
                     ${tools.map(t => `${t.type}: ${t.voxelCount} voxels`).join('<br>')}`, 
                    'success'
                );
                
            } catch (error) {
                log(`Tool generation failed: ${error.message}`, 'error');
                showResult('toolResult', `✗ Tool generation failed: ${error.message}`, 'error');
            }
        };

        // Test 3: Mesh Generation
        window.testMeshGeneration = async function() {
            if (!testGrid) {
                showResult('meshResult', '✗ Run grid creation test first', 'error');
                return;
            }

            log('Testing mesh generation...');
            
            try {
                // Simulate mesh generation timing
                const startTime = performance.now();
                
                // Simulate processing time based on grid size
                const processingTime = Math.min(testGrid.totalVoxels / 10000, 2000); // Max 2 seconds
                
                await new Promise(resolve => setTimeout(resolve, processingTime));
                
                const endTime = performance.now();
                const duration = endTime - startTime;

                // Simulate mesh statistics
                const estimatedVertices = testGrid.totalVoxels * 0.1; // Rough estimate
                const estimatedFaces = estimatedVertices * 0.5;
                const meshMemoryMB = (estimatedVertices * 3 * 4 + estimatedFaces * 3 * 4) / (1024 * 1024);

                log(`Mesh generation completed in ${duration.toFixed(0)}ms`);
                log(`Estimated vertices: ${estimatedVertices.toFixed(0)}`);
                log(`Estimated faces: ${estimatedFaces.toFixed(0)}`);
                log(`Mesh memory: ${meshMemoryMB.toFixed(2)} MB`);

                showResult('meshResult', 
                    `✓ Mesh generated successfully<br>
                     Time: ${duration.toFixed(0)}ms<br>
                     Vertices: ${estimatedVertices.toFixed(0)}<br>
                     Memory: ${meshMemoryMB.toFixed(2)} MB`, 
                    'success'
                );
                
            } catch (error) {
                log(`Mesh generation failed: ${error.message}`, 'error');
                showResult('meshResult', `✗ Mesh generation failed: ${error.message}`, 'error');
            }
        };

        // Test 4: Performance Test
        window.testPerformance = async function() {
            log('Testing performance with different grid sizes...');
            
            try {
                const testSizes = [
                    { name: 'Small', width: 50, height: 50, thickness: 18, resolution: 2.0 },
                    { name: 'Medium', width: 100, height: 100, thickness: 18, resolution: 1.0 },
                    { name: 'Large', width: 200, height: 200, thickness: 18, resolution: 1.0 }
                ];

                const results = [];

                for (const size of testSizes) {
                    const startTime = performance.now();
                    
                    const dimensions = [
                        Math.ceil(size.width / size.resolution),
                        Math.ceil(size.height / size.resolution),
                        Math.ceil(size.thickness / size.resolution)
                    ];

                    const totalVoxels = dimensions[0] * dimensions[1] * dimensions[2];
                    const memoryMB = totalVoxels / (1024 * 1024);

                    // Simulate processing time
                    const processingTime = Math.min(totalVoxels / 50000, 1000);
                    await new Promise(resolve => setTimeout(resolve, processingTime));

                    const endTime = performance.now();
                    const duration = endTime - startTime;

                    results.push({
                        name: size.name,
                        dimensions: dimensions.join('×'),
                        voxels: totalVoxels.toLocaleString(),
                        memory: memoryMB.toFixed(2),
                        time: duration.toFixed(0)
                    });

                    log(`${size.name}: ${dimensions.join('×')} voxels, ${duration.toFixed(0)}ms`);
                }

                const resultHtml = results.map(r => 
                    `${r.name}: ${r.dimensions} (${r.voxels} voxels, ${r.memory}MB, ${r.time}ms)`
                ).join('<br>');

                showResult('perfResult', `✓ Performance test completed<br>${resultHtml}`, 'success');
                
            } catch (error) {
                log(`Performance test failed: ${error.message}`, 'error');
                showResult('perfResult', `✗ Performance test failed: ${error.message}`, 'error');
            }
        };

        // Initialize
        log('Voxel CAM Simple Test initialized');
        log('Click the test buttons to run individual tests');
    </script>
</body>
</html>
