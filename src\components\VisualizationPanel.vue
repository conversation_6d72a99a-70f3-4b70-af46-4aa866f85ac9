<template>
  <div class="visualization-panel">
    <!-- Toolbar -->
    <div v-if="drawCommands.length > 0" class="panel-toolbar">
      <div class="flex items-center space-x-2">
        <!-- View Mode Tabs -->
        <div class="view-tabs">
          <button
            @click="setViewMode('2d')"
            :class="['tab-button', { 'active': viewMode === '2d' }]"
          >
            2D
          </button>
          <button
            @click="setViewMode('tools')"
            :class="['tab-button', { 'active': viewMode === 'tools' }]"
          >
            {{ $t('cncTools.title') }}
          </button>
          <button
            @click="setViewMode('voxel')"
            :class="['tab-button', { 'active': viewMode === 'voxel' }]"
          >
            Voxel CAM
          </button>
        </div>
        <div class="text-xs text-gray-500">
          <span>
            {{ drawCommands.length }} {{ $t('visualization.commands') }}
          </span>
        </div>
      </div>
      <div class="flex items-center space-x-1">
        <!-- 2D Turtle Graphics Controls -->
        <template v-if="viewMode === '2d' && !isTurtleCanvasMinimized">
          <button
            @click="resetView"
            class="p-1 hover:bg-gray-100 rounded text-gray-500 hover:text-gray-700"
            :title="$t('turtleCanvas.resetView')"
          >
            <RotateCcw :size="14" />
          </button>
          <button
            @click="zoomIn"
            class="p-1 hover:bg-gray-100 rounded text-gray-500 hover:text-gray-700"
            :title="$t('turtleCanvas.zoomIn')"
          >
            <ZoomIn :size="14" />
          </button>
          <button
            @click="zoomOut"
            class="p-1 hover:bg-gray-100 rounded text-gray-500 hover:text-gray-700"
            :title="$t('turtleCanvas.zoomOut')"
          >
            <ZoomOut :size="14" />
          </button>
        </template>



        <!-- Separator -->
        <div v-if="viewMode === '2d' && !isTurtleCanvasMinimized" class="w-px h-4 bg-gray-300"></div>

        <!-- Export Controls -->
        <button
          v-if="viewMode === '2d' && !isTurtleCanvasMinimized && drawCommands.length > 0"
          @click="exportToSVG"
          class="p-1 hover:bg-gray-100 rounded text-gray-500 hover:text-gray-700"
          :title="$t('visualization.exportSVG')"
        >
          <Download :size="14" />
        </button>
        <button
          v-if="viewMode === '2d' && !isTurtleCanvasMinimized && drawCommands.length > 0"
          @click="exportToDXF"
          class="p-1 hover:bg-gray-100 rounded text-gray-500 hover:text-gray-700"
          :title="$t('visualization.exportDXF')"
        >
          <Download :size="14" />
        </button>

        <!-- Layer Controls -->
        <button
          v-if="viewMode === '2d' && !isTurtleCanvasMinimized"
          @click="toggleLayerPanel"
          :class="['p-1 hover:bg-gray-100 rounded transition-colors', showLayerPanel ? 'text-blue-600 bg-blue-50' : 'text-gray-500 hover:text-gray-700']"
          :title="$t('layers.togglePanel')"
        >
          <Layers :size="14" />
        </button>

        <!-- Panel Controls -->
        <button
          v-if="viewMode === '2d' && !isTurtleCanvasMinimized"
          @click="minimizeTurtleCanvas"
          class="p-1 hover:bg-gray-100 rounded text-gray-500 hover:text-gray-700"
          :title="$t('turtleCanvas.minimize')"
        >
          <Minimize2 :size="14" />
        </button>
        <button
          @click="clearVisualization"
          class="p-1 hover:bg-gray-100 rounded text-gray-500 hover:text-gray-700"
          :title="$t('visualization.clear')"
        >
          <Trash2 :size="14" />
        </button>
        <button
          @click="toggleExpanded"
          class="p-1 hover:bg-gray-100 rounded text-gray-500 hover:text-gray-700"
          :title="isExpanded ? $t('visualization.collapse') : $t('visualization.expand')"
        >
          <Maximize2 v-if="!isExpanded" :size="14" />
          <Minimize2 v-else :size="14" />
        </button>
      </div>
    </div>

    <!-- Panel Content -->
    <div class="panel-content" :class="{ 'expanded': isExpanded }">
      <div v-if="drawCommands.length === 0" class="no-visualization">
        <div class="text-center py-8 text-gray-500">
          <Palette :size="32" class="mx-auto mb-2 text-gray-400" />
          <p class="text-sm">{{ $t('visualization.noData') }}</p>
          <p class="text-xs text-gray-400 mt-1">{{ $t('visualization.runScriptHint') }}</p>
        </div>
      </div>
      <div v-else-if="viewMode === '2d' && isTurtleCanvasMinimized" class="minimized-turtle-canvas">
        <div class="flex items-center justify-between p-3 bg-gray-50 border border-gray-200 rounded">
          <div class="flex items-center space-x-2">
            <Palette :size="16" class="text-blue-600" />
            <span class="text-sm font-medium text-gray-700">{{ $t('visualization.turtleGraphicsMinimized') }}</span>
            <span class="text-xs text-gray-500">{{ drawCommands.length }} {{ $t('turtleCanvas.commands') }}</span>
          </div>
          <button
            @click="restoreTurtleCanvas"
            class="p-1 hover:bg-gray-100 rounded text-gray-500 hover:text-gray-700"
            :title="$t('visualization.restore')"
          >
            <Maximize2 :size="14" />
          </button>
        </div>
      </div>
      <div v-else-if="viewMode === '2d'" class="turtle-graphics-container">
        <GraphicsCanvas ref="graphicsCanvasRef" :draw-commands="filteredDrawCommands" />
      </div>
      <div v-else-if="viewMode === 'tools'" class="cnc-tools-container">
        <AutoToolDetector />
      </div>
      <div v-else-if="viewMode === 'voxel'" class="voxel-cam-container">
        <VoxelCAMSimulator
          :draw-commands="filteredDrawCommands"
          :door-dimensions="doorDimensions"
        />
      </div>
    </div>

    <!-- Layer Panel -->
    <div v-if="showLayerPanel" class="layer-panel-overlay">
      <LayerPanel
        :layers="layerInfoList"
        @toggle-layer="toggleLayer"
        @toggle-all-layers="toggleAllLayers"
        @close="showLayerPanel = false"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed, onMounted, onUnmounted } from 'vue'
import { invoke } from '@tauri-apps/api/core'
import { Palette, Trash2, Maximize2, Minimize2, RotateCcw, ZoomIn, ZoomOut, Layers, Download } from 'lucide-vue-next'
import GraphicsCanvas from './GraphicsCanvas.vue'
import AutoToolDetector from './AutoToolDetector.vue'
import LayerPanel, { type LayerInfo } from './LayerPanel.vue'
import VoxelCAMSimulator from './VoxelCAMSimulator.vue'
import { layerToolDetector } from '@/services/layerToolDetector'


interface DrawCommand {
  command_type: string
  x1: number
  y1: number
  x2: number
  y2: number
  radius: number
  color: string
  size: number
  text: string
  layer_name: string
  thickness?: number
  start_angle?: number
  end_angle?: number
  clockwise?: boolean  // Arc direction
  svg_path?: string    // SVG path data for complex shapes
  points?: number[][]  // Array of [x, y] points for polylines/polygons
  z1?: number          // Z coordinate for start point or center
  z2?: number          // Z coordinate for end point
}

interface Props {
  drawCommands?: DrawCommand[]
  makerjsJson?: string // JSON output from makerjs_engine
  makerjsSvg?: string  // SVG output from makerjs_engine
}

const props = withDefaults(defineProps<Props>(), {
  drawCommands: () => [],
  makerjsJson: undefined,
  makerjsSvg: undefined
})

const emit = defineEmits<{
  'clear-visualization': []
  'toggle-expanded': [expanded: boolean]
}>()

// Function to convert makerjs JSON to draw commands
const convertMakerjsToDrawCommands = (jsonString: string): DrawCommand[] => {
  try {
    const makerjsModel = JSON.parse(jsonString)
    const commands: DrawCommand[] = []

    // Process paths at root level
    if (makerjsModel.paths) {
      for (const [name, path] of Object.entries(makerjsModel.paths)) {
        commands.push(...convertMakerjsPath(name, path as any, 'default'))
      }
    }

    // Process models (layers)
    if (makerjsModel.models) {
      for (const [layerName, layer] of Object.entries(makerjsModel.models)) {
        const layerData = layer as any

        // Process paths in this layer
        if (layerData.paths) {
          for (const [name, path] of Object.entries(layerData.paths)) {
            commands.push(...convertMakerjsPath(name, path as any, layerName))
          }
        }

        // Process nested models in this layer
        if (layerData.models) {
          for (const [_modelName, model] of Object.entries(layerData.models)) {
            const modelData = model as any
            if (modelData.paths) {
              for (const [name, path] of Object.entries(modelData.paths)) {
                commands.push(...convertMakerjsPath(name, path as any, layerName))
              }
            }
          }
        }
      }
    }

    return commands
  } catch (error) {
    console.error('Error parsing makerjs JSON:', error)
    return []
  }
}

// Function to convert a single makerjs path to draw commands
const convertMakerjsPath = (_name: string, path: any, layerName: string): DrawCommand[] => {
  const commands: DrawCommand[] = []

  // Get layer-specific color (you can customize this logic)
  const layerColor = getLayerColor(layerName)

  switch (path.type) {
    case 'line':
      // Extract 3D coordinates if present
      const z1 = path.origin.length >= 3 ? path.origin[2] : undefined
      const z2 = (path.end_point || path.end).length >= 3 ? (path.end_point || path.end)[2] : undefined

      commands.push({
        command_type: 'line',
        x1: path.origin[0],
        y1: path.origin[1],
        x2: path.end_point ? path.end_point[0] : path.end[0],
        y2: path.end_point ? path.end_point[1] : path.end[1],
        z1: z1, // Z coordinate for start point
        z2: z2, // Z coordinate for end point
        radius: 0,
        color: layerColor,
        size: 1,
        text: '',
        layer_name: layerName
      })
      break

    case 'circle':
      // Extract Z coordinate if present
      const circleZ = path.origin.length >= 3 ? path.origin[2] : undefined

      commands.push({
        command_type: 'circle',
        x1: path.origin[0],
        y1: path.origin[1],
        x2: 0,
        y2: 0,
        z1: circleZ, // Z coordinate for circle center
        radius: path.radius,
        color: layerColor,
        size: 1,
        text: '',
        layer_name: layerName
      })
      break

    case 'arc':
      // Debug output for arc conversion
      console.log('Converting arc path:', path);
      console.log('path.start_angle:', path.start_angle);
      console.log('path.end_angle:', path.end_angle);
      console.log('path.startAngle:', path.startAngle);
      console.log('path.endAngle:', path.endAngle);

      // Fix: Ensure we use the correct property names from makerjs_engine
      const startAngle = path.start_angle !== undefined ? path.start_angle : (path.startAngle !== undefined ? path.startAngle : 0);
      const endAngle = path.end_angle !== undefined ? path.end_angle : (path.endAngle !== undefined ? path.endAngle : 360);

      console.log('Final startAngle:', startAngle);
      console.log('Final endAngle:', endAngle);

      // Extract Z coordinate if present
      const arcZ = path.origin.length >= 3 ? path.origin[2] : undefined

      commands.push({
        command_type: 'arc',
        x1: path.origin[0],
        y1: path.origin[1],
        x2: 0,
        y2: 0,
        z1: arcZ, // Z coordinate for arc center
        radius: path.radius,
        color: layerColor,
        size: 1,
        text: '',
        layer_name: layerName,
        start_angle: endAngle,
        end_angle: startAngle,
        clockwise: path.clockwise || false
      })
      break

    case 'rect':
    case 'rectangle':
      // Convert rectangle to polyline for better SVG compatibility
      const rectPoints = [
        [path.origin[0], path.origin[1]],
        [path.origin[0] + path.width, path.origin[1]],
        [path.origin[0] + path.width, path.origin[1] + path.height],
        [path.origin[0], path.origin[1] + path.height],
        [path.origin[0], path.origin[1]] // Close the rectangle
      ]

      commands.push({
        command_type: 'polygon',
        x1: path.origin[0],
        y1: path.origin[1],
        x2: path.origin[0] + path.width,
        y2: path.origin[1] + path.height,
        radius: 0,
        color: layerColor,
        size: 1,
        text: '',
        layer_name: layerName,
        points: rectPoints
      })
      break

    case 'polyline':
      if (path.points && Array.isArray(path.points)) {
        commands.push({
          command_type: 'polyline',
          x1: path.points[0] ? path.points[0][0] : 0,
          y1: path.points[0] ? path.points[0][1] : 0,
          x2: 0,
          y2: 0,
          radius: 0,
          color: layerColor,
          size: 1,
          text: '',
          layer_name: layerName,
          points: path.points
        })
      }
      break

    case 'bezier':
      if (path.points && Array.isArray(path.points) && path.points.length >= 4) {
        commands.push({
          command_type: 'bezier',
          x1: path.points[0][0],
          y1: path.points[0][1],
          x2: path.points[3][0],
          y2: path.points[3][1],
          radius: 0,
          color: layerColor,
          size: 1,
          text: '',
          layer_name: layerName,
          points: path.points
        })
      }
      break

    default:
      // For unknown path types, try to generate an SVG path
      const svgPath = generateSVGPathFromMakerjs(path)
      if (svgPath) {
        commands.push({
          command_type: 'svg_path',
          x1: path.origin ? path.origin[0] : 0,
          y1: path.origin ? path.origin[1] : 0,
          x2: 0,
          y2: 0,
          radius: 0,
          color: layerColor,
          size: 1,
          text: '',
          layer_name: layerName,
          svg_path: svgPath
        })
      }
      break
  }

  return commands
}

// Enhanced door model color scheme
const getDoorModelColors = () => ({
  // Door panel and structural elements
  'PANEL': '#8B4513',           // Saddle brown for door panel
  'LMM': '#FF6B35',             // Orange for LMM layers

  // Cutting operations (red spectrum)
  'H_Freze': '#FF4500',         // Orange red for top face cuts
  'H_Freze_SF': '#DC143C',      // Crimson for bottom face cuts
  'K_Freze': '#FF6347',         // Tomato for top face grooves
  'K_Freze_SF': '#B22222',      // Fire brick for bottom face grooves

  // V-bit operations (purple spectrum)
  'K_AciliV': '#9932CC',        // Dark orchid for V-bit operations
  'K_AciliV_Pah': '#8A2BE2',    // Blue violet for V-bit chamfers

  // Ball nose operations (blue spectrum)
  'K_Ballnose': '#4169E1',      // Royal blue for ball nose
  'K_Desen': '#1E90FF',         // Dodger blue for decorative patterns

  // Radial operations (green spectrum)
  'H_Raduslu': '#32CD32',       // Lime green for radial operations
  'H_Raduslu_Pah': '#228B22',   // Forest green for radial chamfers

  // Special operations (yellow/orange spectrum)
  'K_Panjur': '#FFD700',        // Gold for shutter operations
  'CLEANCORNERS': '#FFA500',    // Orange for corner cleanup
  'CLEANUP': '#FF8C00',         // Dark orange for general cleanup
  'DEEPEND': '#FF7F50',         // Coral for deep end operations
  'DEEPFRAME': '#FF6347',       // Tomato for deep frame
  'THINFRAME': '#CD853F',       // Peru for thin frame

  // Drilling operations (cyan spectrum)
  'DRILL': '#00CED1',           // Dark turquoise for drilling
  'POCKET': '#20B2AA',          // Light sea green for pockets

  // Default colors
  'default': '#000000',
  'unknown': '#666666'
})

// Helper function to get layer-specific colors with enhanced door model support
const getLayerColor = (layerName: string): string => {
  const doorColors = getDoorModelColors()

  // Direct layer name match
  if (doorColors[layerName as keyof typeof doorColors]) {
    return doorColors[layerName as keyof typeof doorColors]
  }

  // Pattern matching for complex layer names
  const patterns = [
    // H_ operations (contour/profile operations)
    { pattern: /^H_Freze.*_SF$/i, color: doorColors['H_Freze_SF'] },
    { pattern: /^H_Freze/i, color: doorColors['H_Freze'] },
    { pattern: /^H_Raduslu.*_Pah/i, color: doorColors['H_Raduslu_Pah'] },
    { pattern: /^H_Raduslu/i, color: doorColors['H_Raduslu'] },

    // K_ operations (groove/channel operations)
    { pattern: /^K_Freze.*_SF$/i, color: doorColors['K_Freze_SF'] },
    { pattern: /^K_Freze/i, color: doorColors['K_Freze'] },
    { pattern: /^K_AciliV.*_Pah/i, color: doorColors['K_AciliV_Pah'] },
    { pattern: /^K_AciliV/i, color: doorColors['K_AciliV'] },
    { pattern: /^K_Ballnose/i, color: doorColors['K_Ballnose'] },
    { pattern: /^K_Desen/i, color: doorColors['K_Desen'] },
    { pattern: /^K_Panjur/i, color: doorColors['K_Panjur'] },

    // Numeric tool diameter layers
    { pattern: /^(\d+)MM?$/i, color: null }, // Special handling below

    // Special layers
    { pattern: /^LMM/i, color: doorColors['LMM'] },
    { pattern: /CLEANCORNERS/i, color: doorColors['CLEANCORNERS'] },
    { pattern: /CLEANUP/i, color: doorColors['CLEANUP'] },
    { pattern: /DEEPEND/i, color: doorColors['DEEPEND'] },
    { pattern: /DEEPFRAME/i, color: doorColors['DEEPFRAME'] },
    { pattern: /THINFRAME/i, color: doorColors['THINFRAME'] }
  ]

  // Check patterns
  for (const { pattern, color } of patterns) {
    if (pattern.test(layerName)) {
      if (color) return color

      // Special handling for numeric layers
      if (pattern.source.includes('MM')) {
        const numericMatch = layerName.match(/^(\d+)MM?$/i)
        if (numericMatch) {
          const diameter = parseInt(numericMatch[1])
          // Generate color based on tool diameter with better distribution
          const hue = (diameter * 25 + 180) % 360
          return `hsl(${hue}, 75%, 55%)`
        }
      }
    }
  }

  return doorColors['unknown']
}

// Helper function to generate SVG path from Maker.js path data
const generateSVGPathFromMakerjs = (path: any): string | null => {
  try {
    switch (path.type) {
      case 'line':
        const x1 = path.origin[0]
        const y1 = path.origin[1]
        const x2 = path.end_point ? path.end_point[0] : path.end[0]
        const y2 = path.end_point ? path.end_point[1] : path.end[1]
        return `M ${x1} ${y1} L ${x2} ${y2}`

      case 'arc':
        const cx = path.origin[0]
        const cy = path.origin[1]
        const r = path.radius
        // Fix: Use proper null checking for angles (0 is a valid angle!)
        const startAngle = (path.start_angle !== undefined ? path.start_angle : (path.startAngle !== undefined ? path.startAngle : 0)) * Math.PI / 180
        const endAngle = (path.end_angle !== undefined ? path.end_angle : (path.endAngle !== undefined ? path.endAngle : 360)) * Math.PI / 180
        const clockwise = path.clockwise || false

        const x1Arc = cx + r * Math.cos(startAngle)
        const y1Arc = cy + r * Math.sin(startAngle)
        const x2Arc = cx + r * Math.cos(endAngle)
        const y2Arc = cy + r * Math.sin(endAngle)

        // Calculate arc span and determine large arc flag
        let arcSpan = endAngle - startAngle

        // Fix: Handle angle wrapping correctly
        if (clockwise) {
          // For clockwise arcs, if span is positive, we need to go the long way
          if (arcSpan > 0) {
            arcSpan = arcSpan - 2 * Math.PI
          }
        } else {
          // For counter-clockwise arcs, if span is negative, we need to go the long way
          if (arcSpan < 0) {
            arcSpan = arcSpan + 2 * Math.PI
          }
        }

        const largeArcFlag = Math.abs(arcSpan) > Math.PI ? 1 : 0
        const sweepFlag = clockwise ? 1 : 0  // Fix: SVG sweep flag is opposite of our clockwise flag

        return `M ${x1Arc} ${y1Arc} A ${r} ${r} 0 ${largeArcFlag} ${sweepFlag} ${x2Arc} ${y2Arc}`

      case 'circle':
        const cxCircle = path.origin[0]
        const cyCircle = path.origin[1]
        const rCircle = path.radius

        // SVG circle as two arcs
        return `M ${cxCircle - rCircle} ${cyCircle} A ${rCircle} ${rCircle} 0 1 0 ${cxCircle + rCircle} ${cyCircle} A ${rCircle} ${rCircle} 0 1 0 ${cxCircle - rCircle} ${cyCircle}`

      default:
        return null
    }
  } catch (error) {
    console.warn('Failed to generate SVG path for Maker.js path:', path, error)
    return null
  }
}

const drawCommands = ref<DrawCommand[]>([])
const isExpanded = ref(true)  // Start maximized
const isTurtleCanvasMinimized = ref(false)
const viewMode = ref<'2d' | 'tools' | 'voxel'>('2d')
const graphicsCanvasRef = ref<InstanceType<typeof GraphicsCanvas> | null>(null)

// Layer management state
const layerVisibility = ref<Map<string, boolean>>(new Map())
const showLayerPanel = ref(false)

// Door dimensions for voxel CAM
const doorDimensions = computed(() => {
  // Extract door dimensions from draw commands or use defaults
  let width = 600  // Default door width in mm
  let height = 800 // Default door height in mm
  let thickness = 18 // Default door thickness in mm

  // Try to find PANEL layer to get actual dimensions
  const panelCommands = drawCommands.value.filter(cmd =>
    cmd.layer_name.toUpperCase() === 'PANEL'
  )

  if (panelCommands.length > 0) {
    // Find bounding box of panel commands
    let minX = Infinity, maxX = -Infinity
    let minY = Infinity, maxY = -Infinity

    for (const cmd of panelCommands) {
      if (cmd.command_type === 'rectangle') {
        minX = Math.min(minX, cmd.x1, cmd.x2)
        maxX = Math.max(maxX, cmd.x1, cmd.x2)
        minY = Math.min(minY, cmd.y1, cmd.y2)
        maxY = Math.max(maxY, cmd.y1, cmd.y2)
      }
      if (cmd.thickness) {
        thickness = Math.abs(cmd.thickness)
      }
    }

    if (isFinite(minX) && isFinite(maxX)) {
      width = maxX - minX
      height = maxY - minY
    }
  }

  return { width, height, thickness }
})

// Helper function to check if layer should be excluded from visualization
const shouldExcludeLayer = (layerName: string): boolean => {
  const upperLayer = layerName.toUpperCase()

  // Exclude debug and annotation layers
  if (upperLayer === 'LABELS' || upperLayer === 'PARAMETERS' || upperLayer === 'POINTS') {
    return true
  }

  // Exclude LMM layers (measurement/markup)
  if (upperLayer.startsWith('LMM')) {
    return true
  }

  return false
}

// Enhanced door model layer categorization
const categorizeDoorLayer = (layerName: string) => {
  const categories = {
    structural: ['PANEL', 'LMM'],
    cutting: ['H_Freze', 'K_Freze'],
    vbit: ['K_AciliV'],
    ballnose: ['K_Ballnose', 'K_Desen'],
    radial: ['H_Raduslu'],
    special: ['K_Panjur', 'CLEANCORNERS', 'CLEANUP', 'DEEPEND', 'DEEPFRAME', 'THINFRAME'],
    drilling: ['DRILL', 'POCKET']
  }

  for (const [category, patterns] of Object.entries(categories)) {
    if (patterns.some(pattern => layerName.includes(pattern))) {
      return category
    }
  }

  // Check for numeric tool layers
  if (/^\d+MM?$/i.test(layerName)) {
    return 'tool'
  }

  return 'other'
}

// Determine face from layer name and commands
const determineFace = (layerName: string) => {
  // Check for _SF suffix (bottom face)
  if (layerName.includes('_SF')) {
    return 'bottom'
  }

  // Check command positions or other indicators
  // For now, default to top unless explicitly marked as bottom
  return 'top'
}

// Computed properties for enhanced layer management
const detectedLayers = computed(() => {
  const layerMap = new Map<string, {
    commands: DrawCommand[],
    color: string,
    category: string,
    face: string,
    isDoorPanel: boolean
  }>()

  drawCommands.value.forEach(command => {
    const layerName = command.layer_name || 'Default'

    // Skip excluded layers
    if (shouldExcludeLayer(layerName)) {
      return
    }

    if (!layerMap.has(layerName)) {
      const category = categorizeDoorLayer(layerName)
      const isDoorPanel = layerName === 'PANEL'

      layerMap.set(layerName, {
        commands: [],
        color: command.color || getLayerColor(layerName),
        category,
        face: 'top', // Will be determined after all commands are collected
        isDoorPanel
      })
    }
    layerMap.get(layerName)!.commands.push(command)
  })

  return Array.from(layerMap.entries()).map(([name, data]) => {
    const analysis = layerToolDetector.analyzeLayer(name)
    const face = determineFace(name)

    return {
      name,
      commandCount: data.commands.length,
      color: data.color,
      category: data.category,
      face,
      isDoorPanel: data.isDoorPanel,
      toolInfo: analysis.tool ? {
        name: analysis.tool.name,
        shape: analysis.tool.shape || 'cylindrical',
        diameter: analysis.tool.diameter
      } : undefined
    }
  }).sort((a, b) => {
    // Sort order: PANEL first, then by category, then by name
    if (a.isDoorPanel) return -1
    if (b.isDoorPanel) return 1

    const categoryOrder = ['structural', 'cutting', 'vbit', 'ballnose', 'radial', 'special', 'drilling', 'tool', 'other']
    const aCategoryIndex = categoryOrder.indexOf(a.category)
    const bCategoryIndex = categoryOrder.indexOf(b.category)

    if (aCategoryIndex !== bCategoryIndex) {
      return aCategoryIndex - bCategoryIndex
    }

    return a.name.localeCompare(b.name)
  })
})

const layerInfoList = computed((): LayerInfo[] => {
  return detectedLayers.value.map(layer => ({
    ...layer,
    visible: layerVisibility.value.get(layer.name) ?? true
  }))
})

const filteredDrawCommands = computed(() => {
  return drawCommands.value.filter(command => {
    const layerName = command.layer_name || 'Default'

    // Exclude non-visualizable layers
    if (shouldExcludeLayer(layerName)) {
      return false
    }

    return layerVisibility.value.get(layerName) ?? true
  })
})



const clearVisualization = () => {
  drawCommands.value = []
  emit('clear-visualization')
}

const minimizeTurtleCanvas = () => {
  isTurtleCanvasMinimized.value = true
}

const restoreTurtleCanvas = () => {
  isTurtleCanvasMinimized.value = false
}

const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
  emit('toggle-expanded', isExpanded.value)
}

// Graphics canvas control functions
const resetView = () => {
  if (graphicsCanvasRef.value) {
    graphicsCanvasRef.value.resetView()
  }
}

const zoomIn = () => {
  if (graphicsCanvasRef.value) {
    graphicsCanvasRef.value.zoomIn()
  }
}

const zoomOut = () => {
  if (graphicsCanvasRef.value) {
    graphicsCanvasRef.value.zoomOut()
  }
}

// View mode control
const setViewMode = (mode: '2d' | 'tools') => {
  viewMode.value = mode
  // Reset minimized state when switching to tools
  if (mode === 'tools') {
    isTurtleCanvasMinimized.value = false
  }
}

// Keyboard shortcuts for quick access
const handleKeyboardShortcuts = (event: KeyboardEvent) => {
  // Ctrl/Cmd + T for Tools view
  if ((event.ctrlKey || event.metaKey) && event.key === 't') {
    event.preventDefault()
    setViewMode('tools')
  }
  // Ctrl/Cmd + 1-2 for quick view switching
  if ((event.ctrlKey || event.metaKey) && event.key >= '1' && event.key <= '2') {
    event.preventDefault()
    const modes: ('2d' | 'tools')[] = ['2d', 'tools']
    const index = parseInt(event.key) - 1
    if (modes[index]) {
      setViewMode(modes[index])
    }
  }
}



// Layer management functions
const toggleLayerPanel = () => {
  showLayerPanel.value = !showLayerPanel.value
}

const toggleLayer = (layerName: string) => {
  const currentVisibility = layerVisibility.value.get(layerName) ?? true
  layerVisibility.value.set(layerName, !currentVisibility)
}

const toggleAllLayers = (visible: boolean) => {
  detectedLayers.value.forEach(layer => {
    layerVisibility.value.set(layer.name, visible)
  })
}

const initializeLayerVisibility = () => {
  // Initialize visibility for new layers
  detectedLayers.value.forEach(layer => {
    if (!layerVisibility.value.has(layer.name)) {
      layerVisibility.value.set(layer.name, true)
    }
  })
}

// Export current visualization to SVG
const exportToSVG = () => {
  try {
    const svg = generateSVGFromDrawCommands(filteredDrawCommands.value)
    downloadSVG(svg, 'visualization.svg')
  } catch (error) {
    console.error('Failed to export SVG:', error)
  }
}

// Export current visualization to DXF
const exportToDXF = async () => {
  try {
    const dxfContent = await invoke<string>('export_to_dxf', {
      drawCommands: filteredDrawCommands.value
    })
    downloadDXF(dxfContent, 'visualization.dxf')
  } catch (error) {
    console.error('Failed to export DXF:', error)
  }
}

// Generate SVG content from draw commands
const generateSVGFromDrawCommands = (commands: DrawCommand[]): string => {
  if (commands.length === 0) return ''

  // Calculate bounding box
  let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity

  commands.forEach(cmd => {
    const points = cmd.points || [[cmd.x1, cmd.y1], [cmd.x2, cmd.y2]]
    points.forEach(([x, y]) => {
      if (isFinite(x) && isFinite(y)) {
        minX = Math.min(minX, x - (cmd.radius || 0))
        minY = Math.min(minY, y - (cmd.radius || 0))
        maxX = Math.max(maxX, x + (cmd.radius || 0))
        maxY = Math.max(maxY, y + (cmd.radius || 0))
      }
    })
  })

  // Add padding
  const padding = 20
  minX -= padding
  minY -= padding
  maxX += padding
  maxY += padding

  const width = maxX - minX
  const height = maxY - minY

  // Start SVG
  let svg = `<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" width="${width}" height="${height}" viewBox="${minX} ${minY} ${width} ${height}">
  <defs>
    <style>
      .layer-default { stroke: #000000; fill: none; stroke-width: 1; }
      .layer-cut { stroke: #ff0000; fill: none; stroke-width: 1; }
      .layer-engrave { stroke: #0000ff; fill: none; stroke-width: 1; }
      .layer-score { stroke: #00ff00; fill: none; stroke-width: 1; }
      .layer-panel { stroke: #8B4513; fill: none; stroke-width: 2; }
    </style>
  </defs>
  <g transform="scale(1,-1) translate(0,${-maxY - minY})">
`

  // Group commands by layer
  const layerGroups: Record<string, DrawCommand[]> = {}
  commands.forEach(cmd => {
    const layer = cmd.layer_name || 'default'
    if (!layerGroups[layer]) layerGroups[layer] = []
    layerGroups[layer].push(cmd)
  })

  // Generate SVG for each layer
  Object.entries(layerGroups).forEach(([layerName, layerCommands]) => {
    svg += `    <g class="layer-${layerName.toLowerCase()}" data-layer="${layerName}">\n`

    layerCommands.forEach(cmd => {
      svg += generateSVGElement(cmd)
    })

    svg += `    </g>\n`
  })

  svg += `  </g>
</svg>`

  return svg
}

// Generate SVG element for a single draw command
const generateSVGElement = (cmd: DrawCommand): string => {
  const color = cmd.color || '#000000'
  const strokeWidth = cmd.size || 1

  switch (cmd.command_type) {
    case 'line':
      return `      <line x1="${cmd.x1}" y1="${cmd.y1}" x2="${cmd.x2}" y2="${cmd.y2}" stroke="${color}" stroke-width="${strokeWidth}" />\n`

    case 'circle':
      return `      <circle cx="${cmd.x1}" cy="${cmd.y1}" r="${cmd.radius}" stroke="${color}" stroke-width="${strokeWidth}" fill="none" />\n`

    case 'arc':
      // Fix: Use proper null checking for angles (0 is a valid angle!)
      const startAngle = (cmd.start_angle !== undefined ? cmd.start_angle : 0) * Math.PI / 180
      const endAngle = (cmd.end_angle !== undefined ? cmd.end_angle : 360) * Math.PI / 180
      const clockwise = cmd.clockwise || false
      const x1 = cmd.x1 + cmd.radius * Math.cos(startAngle)
      const y1 = cmd.y1 + cmd.radius * Math.sin(startAngle)
      const x2 = cmd.x1 + cmd.radius * Math.cos(endAngle)
      const y2 = cmd.y1 + cmd.radius * Math.sin(endAngle)


      // Calculate arc span and determine flags
      let arcSpan = endAngle - startAngle

      // Fix: Handle angle wrapping correctly for SVG
      if (clockwise) {
        // For clockwise arcs, if span is positive, we need to go the long way
        if (arcSpan > 0) {
          arcSpan = arcSpan - 2 * Math.PI
        }
      } else {
        // For counter-clockwise arcs, if span is negative, we need to go the long way
        if (arcSpan < 0) {
          arcSpan = arcSpan + 2 * Math.PI
        }
      }

      const largeArcFlag = Math.abs(arcSpan) > Math.PI ? 1 : 0
      const sweepFlag = clockwise ? 1 : 0  // Fix: SVG sweep flag is opposite of our clockwise flag
      return `      <path d="M ${x1} ${y1} A ${cmd.radius} ${cmd.radius} 0 ${largeArcFlag} ${sweepFlag} ${x2} ${y2}" stroke="${color}" stroke-width="${strokeWidth}" fill="none" />\n`

    case 'rect':
    case 'rectangle':
      const width = cmd.x2 - cmd.x1
      const height = cmd.y2 - cmd.y1
      return `      <rect x="${cmd.x1}" y="${cmd.y1}" width="${width}" height="${height}" stroke="${color}" stroke-width="${strokeWidth}" fill="none" />\n`

    case 'polyline':
    case 'polygon':
      if (cmd.points && cmd.points.length > 1) {
        const pointsStr = cmd.points.map(p => `${p[0]},${p[1]}`).join(' ')
        const element = cmd.command_type === 'polygon' ? 'polygon' : 'polyline'
        return `      <${element} points="${pointsStr}" stroke="${color}" stroke-width="${strokeWidth}" fill="none" />\n`
      }
      break

    case 'svg_path':
      if (cmd.svg_path) {
        return `      <path d="${cmd.svg_path}" stroke="${color}" stroke-width="${strokeWidth}" fill="none" />\n`
      }
      break

    case 'text':
      return `      <text x="${cmd.x1}" y="${cmd.y1}" font-family="Arial" font-size="${cmd.size || 12}" fill="${color}">${cmd.text}</text>\n`
  }

  return ''
}

// Download SVG file
const downloadSVG = (svgContent: string, filename: string) => {
  const blob = new Blob([svgContent], { type: 'image/svg+xml' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}

// Download DXF file
const downloadDXF = (dxfContent: string, filename: string) => {
  const blob = new Blob([dxfContent], { type: 'application/dxf' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}

// Watch for draw commands prop changes
watch(() => props.drawCommands, (newCommands) => {
  if (newCommands) {
    drawCommands.value = [...newCommands]
    // Initialize layer visibility for new layers
    setTimeout(() => initializeLayerVisibility(), 0)
  }
}, { immediate: true })

// Lifecycle hooks for keyboard shortcuts
onMounted(() => {
  document.addEventListener('keydown', handleKeyboardShortcuts)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeyboardShortcuts)
})

// Watch for makerjs JSON changes
watch(() => props.makerjsJson, (newJson) => {
  if (newJson) {
    console.log('Processing makerjs JSON:', newJson.substring(0, 200) + '...')
    const makerjsCommands = convertMakerjsToDrawCommands(newJson)
    console.log('Converted makerjs JSON to', makerjsCommands.length, 'draw commands')

    // Combine with existing draw commands
    drawCommands.value = [...(props.drawCommands || []), ...makerjsCommands]

    // Initialize layer visibility for new layers
    setTimeout(() => initializeLayerVisibility(), 0)
  }
}, { immediate: true })

// Watch for layer changes to initialize visibility
watch(detectedLayers, () => {
  initializeLayerVisibility()
}, { immediate: true })



// Expose methods for parent component
defineExpose({
  setDrawCommands: (commands: DrawCommand[]) => {
    drawCommands.value = [...commands]
  },
  clearVisualization
})
</script>

<style scoped>
.visualization-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: white;
  position: relative;
}

.panel-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 0.75rem;
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  height: 40px;
}

.panel-content {
  flex: 1;
  overflow: hidden;
  transition: all 0.3s ease;
}

.panel-content.expanded {
  flex: 1;
  /* Cover the whole available area, not modal */
}

.no-visualization {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.turtle-graphics-container {
  height: 100%;
}

.minimized-turtle-canvas {
  padding: 0.5rem;
}

.view-tabs {
  display: flex;
  background-color: #f3f4f6;
  border-radius: 0.375rem;
  padding: 0.25rem;
}

.tab-button {
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 0.25rem;
  transition: colors 0.15s ease-in-out;
  color: #4b5563;
}

.tab-button:hover {
  color: #1f2937;
}

.tab-button.active {
  background-color: white;
  color: #111827;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.three-canvas-container {
  height: 100%;
}

.cnc-tools-container {
  height: 100%;
  overflow-y: auto;
}

.layer-panel-overlay {
  position: absolute;
  top: 3rem;
  right: 1rem;
  z-index: 10;
}
</style>
